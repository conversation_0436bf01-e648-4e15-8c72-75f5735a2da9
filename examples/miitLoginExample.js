/**
 * 工信部登录系统使用示例
 * 网址: https://txjs.miit.gov.cn/sso.action
 */

import MiitLoginApi from '@/validate/miitLogin/utils/miitLoginApi';
import miitLoginHelper from '@/utils/miitLoginHelper';

// ==================== 基础API使用示例 ====================

/**
 * 示例1: 基础登录流程
 */
async function basicLoginExample() {
    const loginApi = new MiitLoginApi();
    
    const credentials = {
        username: '你的用户名',  // 登录名/统一社会信用代码/身份证号
        password: '你的密码',
        toUrl: 'http://txjs.miit.gov.cn'  // 可选，登录成功后跳转的URL
    };

    try {
        console.log('=== 基础登录流程示例 ===');
        
        // 步骤1: 获取短信验证码
        console.log('步骤1: 获取短信验证码');
        const smsResult = await loginApi.getSmsCode({
            username: credentials.username,
            password: credentials.password
        });
        
        if (!smsResult.success) {
            console.error('获取短信验证码失败:', smsResult.message);
            return;
        }
        
        console.log('短信验证码发送成功:', smsResult.message);
        
        // 步骤2: 等待用户输入验证码（实际使用中需要用户界面）
        const smsCode = '123456'; // 这里需要用户输入实际收到的验证码
        
        // 步骤3: 执行登录
        console.log('步骤2: 执行登录');
        const loginResult = await loginApi.login({
            username: credentials.username,
            password: credentials.password,
            smsCode: smsCode,
            toUrl: credentials.toUrl
        });
        
        if (loginResult.success) {
            console.log('登录成功!');
            console.log('跳转URL:', loginResult.redirectUrl);
            // window.location.href = loginResult.redirectUrl;
        } else {
            console.error('登录失败:', loginResult.message);
            handleLoginError(loginResult);
        }
        
    } catch (error) {
        console.error('登录过程发生错误:', error);
    }
}

/**
 * 示例2: 使用登录流程封装
 */
async function loginFlowExample() {
    const loginApi = new MiitLoginApi();
    
    const credentials = {
        username: '你的用户名',
        password: '你的密码',
        toUrl: 'http://txjs.miit.gov.cn'
    };

    try {
        console.log('=== 登录流程封装示例 ===');
        
        const flowResult = await loginApi.loginFlow(credentials);
        
        if (flowResult.success) {
            console.log(flowResult.message);
            
            // 等待用户输入验证码
            const smsCode = prompt('请输入收到的短信验证码:');
            
            if (smsCode) {
                const loginResult = await flowResult.loginFunction(smsCode);
                
                if (loginResult.success) {
                    console.log('登录成功!');
                    window.location.href = loginResult.redirectUrl;
                } else {
                    console.error('登录失败:', loginResult.message);
                }
            }
        } else {
            console.error('登录流程失败:', flowResult.message);
        }
        
    } catch (error) {
        console.error('登录流程发生错误:', error);
    }
}

// ==================== 高级助手使用示例 ====================

/**
 * 示例3: 使用登录助手显示对话框
 */
function loginDialogExample() {
    console.log('=== 登录对话框示例 ===');
    
    miitLoginHelper.showLoginDialog({
        title: '工信部系统登录',
        toUrl: 'http://txjs.miit.gov.cn',
        onSuccess: (result) => {
            console.log('登录成功回调:', result);
            // 可以在这里处理登录成功后的逻辑
            alert('登录成功！即将跳转...');
            window.location.href = result.redirectUrl;
        },
        onError: (error) => {
            console.error('登录失败回调:', error);
            // 可以在这里处理登录失败的逻辑
        }
    });
}

/**
 * 示例4: 快速登录（自动化脚本使用）
 */
async function quickLoginExample() {
    console.log('=== 快速登录示例 ===');
    
    const credentials = {
        username: '你的用户名',
        password: '你的密码',
        toUrl: 'http://txjs.miit.gov.cn'
    };
    
    const result = await miitLoginHelper.quickLogin(credentials);
    
    if (result.success) {
        console.log(result.message);
        
        // 这里可以等待用户输入验证码，然后调用 result.loginFunction
        // const smsCode = await getUserInput('请输入短信验证码:');
        // const loginResult = await result.loginFunction(smsCode);
    } else {
        console.error('快速登录失败:', result.message);
    }
}

/**
 * 示例5: 检查登录状态
 */
async function checkLoginStatusExample() {
    console.log('=== 检查登录状态示例 ===');
    
    const status = await miitLoginHelper.checkLoginStatus();
    
    if (status.success) {
        console.log('登录状态检查成功:', status.data);
    } else {
        console.error('登录状态检查失败:', status.message);
    }
}

// ==================== 工具函数 ====================

/**
 * 处理登录错误
 * @param {Object} result - 登录结果
 */
function handleLoginError(result) {
    switch (true) {
        case result.needCompleteInfo:
            if (confirm('企业需完善企业通用基础信息表，是否前往完善？')) {
                window.open(result.completeInfoUrl);
            }
            if (result.redirectUrl) {
                window.location.href = result.redirectUrl;
            }
            break;
            
        case result.needAgreeProtocol:
            alert('需要同意用户协议才能继续');
            // 这里可以打开协议页面
            break;
            
        case result.needFinishRegister:
            alert('需要完成注册流程');
            // 这里可以跳转到注册页面
            break;
            
        case result.needResetPassword:
            if (confirm('需要重置密码，是否前往重置？')) {
                window.location.href = result.resetPasswordUrl;
            }
            break;
            
        default:
            alert(`登录失败: ${result.message}`);
            break;
    }
}

/**
 * 获取用户输入（示例函数）
 * @param {string} message - 提示信息
 * @returns {Promise<string>} 用户输入
 */
function getUserInput(message) {
    return new Promise((resolve) => {
        const input = prompt(message);
        resolve(input);
    });
}

// ==================== 在页面中使用 ====================

/**
 * 在Vue组件中使用示例
 */
const VueComponentExample = {
    data() {
        return {
            loginForm: {
                username: '',
                password: '',
                smsCode: ''
            },
            isGettingSms: false,
            isLogging: false
        };
    },
    methods: {
        async getSmsCode() {
            if (!this.loginForm.username || !this.loginForm.password) {
                this.$message.warning('请先输入用户名和密码');
                return;
            }
            
            this.isGettingSms = true;
            
            try {
                const loginApi = new MiitLoginApi();
                const result = await loginApi.getSmsCode({
                    username: this.loginForm.username,
                    password: this.loginForm.password
                });
                
                if (result.success) {
                    this.$message.success(result.message);
                } else {
                    this.$message.error(result.message);
                }
            } catch (error) {
                this.$message.error('获取验证码失败');
            } finally {
                this.isGettingSms = false;
            }
        },
        
        async login() {
            if (!this.loginForm.username || !this.loginForm.password || !this.loginForm.smsCode) {
                this.$message.warning('请填写完整的登录信息');
                return;
            }
            
            this.isLogging = true;
            
            try {
                const loginApi = new MiitLoginApi();
                const result = await loginApi.login({
                    username: this.loginForm.username,
                    password: this.loginForm.password,
                    smsCode: this.loginForm.smsCode
                });
                
                if (result.success) {
                    this.$message.success('登录成功');
                    window.location.href = result.redirectUrl;
                } else {
                    this.$message.error(result.message);
                    // 处理特殊情况...
                }
            } catch (error) {
                this.$message.error('登录失败');
            } finally {
                this.isLogging = false;
            }
        }
    }
};

// 导出示例函数
export {
    basicLoginExample,
    loginFlowExample,
    loginDialogExample,
    quickLoginExample,
    checkLoginStatusExample,
    VueComponentExample
};
