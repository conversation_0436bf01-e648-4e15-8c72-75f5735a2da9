<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工信部登录API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #409eff;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid #ebeef5;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #303133;
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #66b1ff;
        }
        .button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .input {
            width: 200px;
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .result {
            background: #f5f7fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #67c23a;
            background: #f0f9ff;
        }
        .error {
            border-left: 4px solid #f56c6c;
            background: #fef0f0;
        }
        .warning {
            border-left: 4px solid #e6a23c;
            background: #fdf6ec;
        }
        .info {
            border-left: 4px solid #409eff;
            background: #ecf5ff;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status.success { background: #67c23a; color: white; }
        .status.error { background: #f56c6c; color: white; }
        .status.warning { background: #e6a23c; color: white; }
        .status.info { background: #409eff; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔌 工信部登录API测试</h1>
            <p>测试修改后的API是否能正常工作（无CORS问题）</p>
        </div>

        <!-- 环境信息 -->
        <div class="test-section">
            <h3>📋 环境信息</h3>
            <div id="env-info">检测中...</div>
        </div>

        <!-- 连接测试 -->
        <div class="test-section">
            <h3>🔗 连接测试</h3>
            <button class="button" onclick="testConnection()">测试连接</button>
            <span id="connection-status" class="status info">未测试</span>
            <div id="connection-result" class="result" style="display: none;"></div>
        </div>

        <!-- 短信API测试 -->
        <div class="test-section">
            <h3>📱 短信API测试</h3>
            <input type="text" class="input" id="test-username" placeholder="测试用户名" value="test-user">
            <input type="password" class="input" id="test-password" placeholder="测试密码" value="test-pass">
            <button class="button" onclick="testSmsApi()">测试短信API</button>
            <span id="sms-status" class="status info">未测试</span>
            <div id="sms-result" class="result" style="display: none;"></div>
        </div>

        <!-- 完整测试 -->
        <div class="test-section">
            <h3>🧪 完整测试</h3>
            <button class="button" onclick="runFullTest()">运行完整测试</button>
            <span id="full-status" class="status info">未开始</span>
            <div id="full-result" class="result" style="display: none;"></div>
        </div>

        <!-- 控制台日志 -->
        <div class="test-section">
            <h3>📝 控制台日志</h3>
            <button class="button" onclick="clearLogs()">清除日志</button>
            <button class="button" onclick="toggleLogs()">显示/隐藏日志</button>
            <div id="console-logs" class="result" style="display: none; max-height: 200px;"></div>
        </div>
    </div>

    <!-- 加载CryptoJS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    
    <script>
        // 简化版的MiitLoginApi，直接嵌入页面
        class MiitLoginApi {
            constructor() {
                this.baseUrl = 'http://ucenter.miit.gov.cn';
                this.loginUrl = `${this.baseUrl}/login.action`;
                this.sendMessageUrl = `${this.baseUrl}/sendMessage.action`;
                console.log('🔌 工信部登录API已初始化 - 使用fetch避免CORS问题');
            }

            md5Encrypt(password) {
                return CryptoJS.MD5(password).toString();
            }

            async testConnection() {
                try {
                    console.log('🧪 测试连接到工信部服务器...');
                    
                    const response = await fetch(`${this.baseUrl}/login.jsp`, {
                        method: 'HEAD',
                        mode: 'cors',
                        credentials: 'include'
                    });

                    const result = {
                        success: response.ok,
                        status: response.status,
                        statusText: response.statusText,
                        message: response.ok ? '连接正常' : `连接失败: ${response.status} ${response.statusText}`
                    };

                    console.log('🧪 连接测试结果:', result);
                    return result;

                } catch (error) {
                    console.error('❌ 连接测试失败:', error);
                    return {
                        success: false,
                        message: `连接失败: ${error.message}`,
                        error: error
                    };
                }
            }

            async sendRequest(url, data) {
                const fullUrl = this.baseUrl + url;
                
                try {
                    console.log('🚀 发送请求:', fullUrl);
                    
                    const response = await fetch(fullUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: data,
                        credentials: 'include',
                        mode: 'cors'
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    let responseData;
                    const contentType = response.headers.get('content-type');
                    
                    if (contentType && contentType.includes('application/json')) {
                        responseData = await response.json();
                    } else {
                        const text = await response.text();
                        try {
                            responseData = JSON.parse(text);
                        } catch (e) {
                            console.warn('响应不是有效的JSON，返回原始文本');
                            responseData = { message: text, rawResponse: text };
                        }
                    }

                    console.log('✅ 请求成功:', response.status, responseData);
                    return { data: responseData, status: response.status };

                } catch (error) {
                    console.error('❌ 请求失败:', error);
                    throw error;
                }
            }

            async getSmsCode({ username, password, logintype = 'sendMessage' }) {
                try {
                    if (!username || !password) {
                        throw new Error('账号或密码不能为空！');
                    }

                    const encryptedPassword = this.md5Encrypt(password);
                    const params = new URLSearchParams({
                        logintype: logintype,
                        username: username.trim(),
                        password: encryptedPassword
                    });

                    console.log('发送短信验证码请求:', { username, logintype });
                    const response = await this.sendRequest('/sendMessage.action', params);
                    
                    if (response.data.result === 0) {
                        return {
                            success: true,
                            message: `已发送到手机号：${response.data.msg}，请注意查收`,
                            data: response.data
                        };
                    } else if (response.data.result === 999) {
                        throw new Error('短信通道繁忙，请稍后再试(101)！');
                    } else {
                        throw new Error(response.data.msg || '发送短信验证码失败');
                    }
                } catch (error) {
                    console.error('获取短信验证码失败:', error);
                    return {
                        success: false,
                        message: error.message,
                        error: error
                    };
                }
            }
        }

        // 全局API实例
        const api = new MiitLoginApi();
        
        // 日志收集
        const logs = [];
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            logs.push({ type: 'log', time: new Date().toLocaleTimeString(), args });
            updateConsoleLogs();
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push({ type: 'error', time: new Date().toLocaleTimeString(), args });
            updateConsoleLogs();
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            logs.push({ type: 'warn', time: new Date().toLocaleTimeString(), args });
            updateConsoleLogs();
            originalConsoleWarn.apply(console, args);
        };

        // 初始化
        window.onload = function() {
            showEnvironmentInfo();
        };

        function showEnvironmentInfo() {
            const isExtension = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
            const info = `
环境类型: ${isExtension ? '🔌 浏览器插件环境' : '🌐 普通网页环境'}
当前域名: ${window.location.origin}
用户代理: ${navigator.userAgent.substring(0, 80)}...
支持fetch: ${typeof fetch !== 'undefined' ? '✅' : '❌'}
            `.trim();
            
            document.getElementById('env-info').textContent = info;
        }

        async function testConnection() {
            const button = event.target;
            const status = document.getElementById('connection-status');
            const result = document.getElementById('connection-result');
            
            button.disabled = true;
            button.textContent = '测试中...';
            status.textContent = '测试中';
            status.className = 'status warning';
            
            try {
                const testResult = await api.testConnection();
                
                if (testResult.success) {
                    status.textContent = '连接成功';
                    status.className = 'status success';
                    result.className = 'result success';
                } else {
                    status.textContent = '连接失败';
                    status.className = 'status error';
                    result.className = 'result error';
                }
                
                result.textContent = JSON.stringify(testResult, null, 2);
                result.style.display = 'block';
                
            } catch (error) {
                status.textContent = '测试异常';
                status.className = 'status error';
                result.className = 'result error';
                result.textContent = `异常: ${error.message}`;
                result.style.display = 'block';
            } finally {
                button.disabled = false;
                button.textContent = '测试连接';
            }
        }

        async function testSmsApi() {
            const button = event.target;
            const status = document.getElementById('sms-status');
            const result = document.getElementById('sms-result');
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            
            button.disabled = true;
            button.textContent = '测试中...';
            status.textContent = '测试中';
            status.className = 'status warning';
            
            try {
                const testResult = await api.getSmsCode({ username, password });
                
                if (testResult.success) {
                    status.textContent = 'API调用成功';
                    status.className = 'status success';
                    result.className = 'result success';
                } else {
                    status.textContent = 'API返回错误';
                    status.className = 'status warning';
                    result.className = 'result warning';
                }
                
                result.textContent = JSON.stringify(testResult, null, 2);
                result.style.display = 'block';
                
            } catch (error) {
                status.textContent = '测试异常';
                status.className = 'status error';
                result.className = 'result error';
                result.textContent = `异常: ${error.message}`;
                result.style.display = 'block';
            } finally {
                button.disabled = false;
                button.textContent = '测试短信API';
            }
        }

        async function runFullTest() {
            const button = event.target;
            const status = document.getElementById('full-status');
            const result = document.getElementById('full-result');
            
            button.disabled = true;
            button.textContent = '测试中...';
            status.textContent = '运行中';
            status.className = 'status warning';
            
            const testResults = {
                startTime: new Date().toISOString(),
                tests: []
            };
            
            try {
                // 测试1: 连接测试
                console.log('=== 开始完整测试 ===');
                testResults.tests.push({ name: '连接测试', status: '开始' });
                const connectionResult = await api.testConnection();
                testResults.tests[0] = { name: '连接测试', status: connectionResult.success ? '成功' : '失败', result: connectionResult };
                
                // 测试2: 短信API测试
                testResults.tests.push({ name: '短信API测试', status: '开始' });
                const smsResult = await api.getSmsCode({ 
                    username: document.getElementById('test-username').value,
                    password: document.getElementById('test-password').value
                });
                testResults.tests[1] = { name: '短信API测试', status: smsResult.success ? '成功' : '失败', result: smsResult };
                
                testResults.endTime = new Date().toISOString();
                testResults.summary = {
                    total: testResults.tests.length,
                    success: testResults.tests.filter(t => t.status === '成功').length,
                    failed: testResults.tests.filter(t => t.status === '失败').length
                };
                
                if (testResults.summary.failed === 0) {
                    status.textContent = '全部通过';
                    status.className = 'status success';
                    result.className = 'result success';
                } else {
                    status.textContent = '部分失败';
                    status.className = 'status warning';
                    result.className = 'result warning';
                }
                
                result.textContent = JSON.stringify(testResults, null, 2);
                result.style.display = 'block';
                
            } catch (error) {
                status.textContent = '测试异常';
                status.className = 'status error';
                result.className = 'result error';
                result.textContent = `测试异常: ${error.message}`;
                result.style.display = 'block';
            } finally {
                button.disabled = false;
                button.textContent = '运行完整测试';
            }
        }

        function updateConsoleLogs() {
            const logsDiv = document.getElementById('console-logs');
            const recentLogs = logs.slice(-50); // 只显示最近50条
            
            logsDiv.innerHTML = recentLogs.map(log => {
                const color = log.type === 'error' ? '#f56c6c' : log.type === 'warn' ? '#e6a23c' : '#409eff';
                const argsStr = log.args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                ).join(' ');
                return `<div style="color: ${color};">[${log.time}] ${argsStr}</div>`;
            }).join('');
            
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            logs.length = 0;
            updateConsoleLogs();
        }

        function toggleLogs() {
            const logsDiv = document.getElementById('console-logs');
            logsDiv.style.display = logsDiv.style.display === 'none' ? 'block' : 'none';
        }
    </script>
</body>
</html>
