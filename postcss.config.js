module.exports = {
    plugins: {
        // 给 Element UI 样式添加唯一前缀（如 "plugin-"）
        'postcss-prefix-selector': {
            prefix: '#plugin-shadow-root', // 前缀选择器
            includeFiles: [/element-ui/], // 仅处理 Element UI 的样式
            transform(prefix, selector) {
                // 避免给 body 等全局选择器添加前缀
                if (selector === 'body') {
                    return selector;
                }
                return `${prefix} ${selector}`;
            }
        }
    }
};