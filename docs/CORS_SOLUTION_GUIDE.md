# 工信部登录跨域问题解决方案

## 问题描述

在开发环境中访问工信部登录API时遇到CORS跨域错误：
```
Access to XMLHttpRequest at 'http://ucenter.miit.gov.cn/sendMessage.action' 
from origin 'http://localhost:9527' has been blocked by CORS policy: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 解决方案

### 方案1: 开发代理配置（推荐用于开发环境）

#### 1.1 修改 vue.config.js

已为您配置了代理设置：

```javascript
// vue.config.js
devServer: {
    proxy: {
        '/miit-api': {
            target: 'http://ucenter.miit.gov.cn',
            changeOrigin: true,
            pathRewrite: {
                '^/miit-api': ''
            },
            headers: {
                'Referer': 'http://ucenter.miit.gov.cn/',
                'Origin': 'http://ucenter.miit.gov.cn',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            secure: false,
            logLevel: 'debug',
            cookieDomainRewrite: {
                'ucenter.miit.gov.cn': 'localhost'
            }
        }
    }
}
```

#### 1.2 重启开发服务器

```bash
npm run serve
# 或
yarn serve
```

#### 1.3 使用代理API

现在API会自动使用代理：
- 开发环境: `/miit-api/sendMessage.action`
- 生产环境: `http://ucenter.miit.gov.cn/sendMessage.action`

### 方案2: Chrome扩展版本（推荐用于生产环境）

#### 2.1 使用内容脚本

已创建 `src/content/miitLoginContent.js`，可以直接在工信部登录页面注入：

```javascript
// 在工信部登录页面控制台执行
const script = document.createElement('script');
script.src = 'path/to/miitLoginContent.js';
document.head.appendChild(script);
```

#### 2.2 使用扩展API

已创建 `src/api/miitLoginApiExtension.js`，使用fetch绕过CORS：

```javascript
import MiitLoginApiExtension from '@/api/miitLoginApiExtension';

const api = new MiitLoginApiExtension();

// 获取验证码
const smsResult = await api.getSmsCode({
    username: 'your-username',
    password: 'your-password'
});

// 登录
const loginResult = await api.login({
    username: 'your-username',
    password: 'your-password',
    smsCode: '123456'
});
```

### 方案3: 浏览器设置（临时解决方案）

#### 3.1 Chrome浏览器

启动Chrome时添加参数禁用CORS检查：

```bash
# Windows
chrome.exe --user-data-dir="C:/Chrome dev session" --disable-web-security --disable-features=VizDisplayCompositor

# macOS
open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args --user-data-dir="/tmp/chrome_dev_test" --disable-web-security --disable-features=VizDisplayCompositor

# Linux
google-chrome --user-data-dir="/tmp/chrome_dev_test" --disable-web-security --disable-features=VizDisplayCompositor
```

⚠️ **注意**: 这种方法仅用于开发测试，不要在正常浏览中使用。

#### 3.2 Firefox浏览器

1. 在地址栏输入 `about:config`
2. 搜索 `security.tls.insecure_fallback_hosts`
3. 添加 `ucenter.miit.gov.cn`

### 方案4: 使用CORS浏览器扩展

安装CORS扩展（仅用于开发）：
- Chrome: "CORS Unblock" 或 "Moesif Origin & CORS Changer"
- Firefox: "CORS Everywhere"

## 测试步骤

### 1. 测试代理配置

```javascript
// 在浏览器控制台测试
fetch('/miit-api/sendMessage.action', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'logintype=sendMessage&username=test&password=test'
})
.then(response => response.json())
.then(data => console.log('代理测试结果:', data))
.catch(error => console.error('代理测试失败:', error));
```

### 2. 测试Vue组件

```vue
<template>
  <div>
    <el-button @click="testLogin">测试登录</el-button>
  </div>
</template>

<script>
import MiitLoginApi from '@/api/miitLoginApi';

export default {
  methods: {
    async testLogin() {
      const api = new MiitLoginApi();
      
      try {
        const result = await api.getSmsCode({
          username: 'test-username',
          password: 'test-password'
        });
        
        console.log('测试结果:', result);
        this.$message.success('API调用成功');
      } catch (error) {
        console.error('测试失败:', error);
        this.$message.error('API调用失败: ' + error.message);
      }
    }
  }
};
</script>
```

### 3. 测试内容脚本

1. 打开工信部登录页面: https://txjs.miit.gov.cn/sso.action
2. 在控制台执行：

```javascript
// 加载内容脚本
const script = document.createElement('script');
script.textContent = `
    // 这里粘贴 miitLoginContent.js 的内容
`;
document.head.appendChild(script);
```

## 常见问题

### Q1: 代理配置后仍然报CORS错误

**解决方案:**
1. 确保重启了开发服务器
2. 检查vue.config.js语法是否正确
3. 查看控制台是否有代理日志

### Q2: 扩展版本无法发送请求

**解决方案:**
1. 确保在HTTPS环境或Chrome扩展中使用
2. 检查Content Security Policy设置
3. 使用fetch而不是axios

### Q3: 登录成功但无法跳转

**解决方案:**
1. 检查返回的redirectUrl
2. 确保目标域名可访问
3. 处理跨域跳转限制

### Q4: 开发环境正常，生产环境失败

**解决方案:**
1. 生产环境需要配置服务器代理
2. 或使用Chrome扩展版本
3. 或部署到同域名下

## 推荐使用方式

### 开发环境
使用 **方案1: 开发代理配置**
- 配置简单
- 调试方便
- 完全模拟生产环境

### 生产环境
使用 **方案2: Chrome扩展版本**
- 无需服务器配置
- 用户体验好
- 安全性高

### 临时测试
使用 **方案3: 浏览器设置**
- 快速验证
- 无需代码修改
- 仅用于测试

## 安全注意事项

1. **不要在生产环境禁用CORS检查**
2. **保护用户登录凭据**
3. **使用HTTPS传输敏感信息**
4. **定期更新依赖包**
5. **遵循最小权限原则**

## 更新日志

- v1.0.0: 初始版本，添加代理配置
- v1.1.0: 添加Chrome扩展支持
- v1.2.0: 完善错误处理和文档
