# CORS预检请求问题解决方案

## 问题分析

您遇到的错误信息：
```
CORS policy: Response to preflight request doesn't pass access control check: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

这表明浏览器发送了**预检请求（preflight request）**，但服务器没有正确响应。

## 什么是预检请求？

浏览器在以下情况会发送OPTIONS预检请求：

### 1. 使用了非简单请求方法
- 简单方法：`GET`, `HEAD`, `POST`
- 非简单方法：`PUT`, `DELETE`, `PATCH` 等

### 2. 包含了非简单请求头
- 简单头部：`Accept`, `Accept-Language`, `Content-Language`, `Content-Type`（限定值）
- 非简单头部：`X-Requested-With`, `Authorization`, 自定义头部

### 3. Content-Type不是简单类型
- 简单类型：`text/plain`, `multipart/form-data`, `application/x-www-form-urlencoded`
- 非简单类型：`application/json`, `application/xml` 等

## 您代码中的问题

### 原始代码问题：
```javascript
// ❌ 这些配置会触发预检请求
headers: {
    'X-Requested-With': 'XMLHttpRequest', // 🚫 非简单头部
    'Accept': 'application/json, text/javascript, */*; q=0.01' // 🚫 复杂Accept
},
withCredentials: true // 🚫 跨域时需要服务器支持
```

### 修复后的代码：
```javascript
// ✅ 避免预检请求的配置
headers: {
    'Content-Type': 'application/x-www-form-urlencoded' // ✅ 简单头部
    // 移除 X-Requested-With
    // 简化 Accept
},
withCredentials: false // ✅ 开发环境不需要凭据
```

## 解决方案

### 1. 代理配置（已修复）

**vue.config.js** 中的代理配置：
```javascript
proxy: {
    '/miit-api': {
        target: 'http://ucenter.miit.gov.cn',
        changeOrigin: true,
        pathRewrite: { '^/miit-api': '' },
        // 处理预检请求
        onProxyReq: (proxyReq, req, res) => {
            proxyReq.removeHeader('X-Requested-With');
        },
        onProxyRes: (proxyRes, req, res) => {
            proxyRes.headers['Access-Control-Allow-Origin'] = '*';
            proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS';
            proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Accept';
        }
    }
}
```

### 2. API请求优化（已修复）

**多策略请求方法**：
```javascript
async sendRequest(url, data) {
    try {
        // 开发环境：使用代理
        if (this.isDevelopment) {
            return await this.client.post(url, data, {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                withCredentials: false
            });
        }
        
        // 生产环境：使用多策略
        return await corsHelper.sendRequest(fullUrl, data);
        
    } catch (error) {
        // 智能诊断和建议
        const diagnosis = diagnoseCorsIssue(error);
        throw new Error(`${error.message}\n建议: ${diagnosis.suggestions.join(', ')}`);
    }
}
```

### 3. 简单请求策略

**避免预检请求的最佳实践**：
```javascript
// ✅ 简单请求配置
const simpleConfig = {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: new URLSearchParams(data),
    credentials: 'same-origin' // 或 'omit'
};

fetch(url, simpleConfig);
```

## 使用步骤

### 1. 重启开发服务器
```bash
npm run serve
# 或
yarn serve
```

### 2. 验证代理配置
在浏览器控制台测试：
```javascript
// 测试代理连接
fetch('/miit-api/login.jsp')
    .then(response => console.log('代理正常:', response.status))
    .catch(error => console.error('代理失败:', error));
```

### 3. 使用优化后的API
```javascript
import MiitLoginApi from '@/validate/miitLogin/utils/miitLoginApi';

const api = new MiitLoginApi();

// 现在不会有预检请求问题
const result = await api.getSmsCode({
    username: 'test',
    password: 'test'
});
```

## 调试技巧

### 1. 检查网络请求
在开发者工具的Network标签中：
- 🔍 查看是否有OPTIONS请求
- 🔍 检查请求头是否包含非简单头部
- 🔍 确认响应头是否包含CORS头部

### 2. 使用CORS助手
```javascript
import { corsHelper } from '@/utils/corsHelper';

// 测试所有策略
const results = await corsHelper.testAllStrategies();
console.log('策略测试结果:', results);

// 诊断CORS问题
try {
    await api.getSmsCode(params);
} catch (error) {
    const diagnosis = diagnoseCorsIssue(error);
    console.log('问题诊断:', diagnosis);
}
```

### 3. 环境检测
```javascript
import { detectCorsEnvironment } from '@/utils/corsHelper';

const env = detectCorsEnvironment();
console.log('当前环境:', env);
```

## 常见问题

### Q1: 为什么移除了X-Requested-With头部？
**A:** 这个头部会触发预检请求，而工信部服务器不支持预检请求。移除后可以发送简单请求。

### Q2: withCredentials设置为false会影响登录吗？
**A:** 开发环境使用代理时不需要凭据。生产环境会根据情况自动调整。

### Q3: 代理配置后仍然报错怎么办？
**A:** 
1. 确保重启了开发服务器
2. 检查代理路径是否正确（/miit-api）
3. 查看控制台是否有代理日志

### Q4: 生产环境如何处理CORS？
**A:** 
1. 使用Chrome扩展环境（推荐）
2. 配置服务器代理
3. 使用多策略请求助手

## 验证方法

### 1. 检查请求类型
```javascript
// 在Network标签中应该看到：
// ✅ POST /miit-api/sendMessage.action (没有OPTIONS请求)
// ❌ OPTIONS /miit-api/sendMessage.action (有预检请求)
```

### 2. 测试API调用
```javascript
// 应该成功调用，没有CORS错误
const api = new MiitLoginApi();
const result = await api.getSmsCode({
    username: 'test',
    password: 'test'
});
console.log('API调用结果:', result);
```

### 3. 使用测试页面
访问测试页面验证：
```
http://localhost:8080/#/miit-login-test
```

## 总结

通过以下修改解决了CORS预检请求问题：

1. ✅ **移除触发预检请求的头部** - `X-Requested-With`
2. ✅ **简化请求配置** - 只使用简单头部
3. ✅ **配置代理处理** - 自动处理CORS头部
4. ✅ **多策略降级** - 自动选择最佳请求方式
5. ✅ **智能诊断** - 提供具体的错误建议

现在您的API调用应该不会再遇到CORS预检请求问题了！
