# 工信部统一身份认证系统登录封装

本封装提供了对工信部统一身份认证系统 (https://txjs.miit.gov.cn/sso.action) 的完整登录功能封装。

## 功能特性

- ✅ 完整的登录流程封装
- ✅ 短信验证码获取
- ✅ 密码MD5加密
- ✅ 多种登录结果处理
- ✅ 用户友好的对话框界面
- ✅ 错误处理和状态管理
- ✅ TypeScript类型支持
- ✅ 自动化脚本支持

## 文件结构

```
src/
├── api/
│   └── miitLoginApi.js          # 核心登录API封装
├── utils/
│   └── miitLoginHelper.js       # 登录助手工具类
examples/
└── miitLoginExample.js          # 使用示例
docs/
└── MIIT_LOGIN_README.md         # 本文档
```

## 快速开始

### 1. 基础使用

```javascript
import MiitLoginApi from '@/api/miitLoginApi';

const loginApi = new MiitLoginApi();

// 获取短信验证码
const smsResult = await loginApi.getSmsCode({
    username: '你的用户名',
    password: '你的密码'
});

if (smsResult.success) {
    console.log(smsResult.message); // "已发送到手机号：138****1234，请注意查收"
    
    // 用户输入验证码后执行登录
    const loginResult = await loginApi.login({
        username: '你的用户名',
        password: '你的密码',
        smsCode: '123456'
    });
    
    if (loginResult.success) {
        window.location.href = loginResult.redirectUrl;
    }
}
```

### 2. 使用登录助手（推荐）

```javascript
import miitLoginHelper from '@/utils/miitLoginHelper';

// 显示登录对话框
miitLoginHelper.showLoginDialog({
    title: '工信部系统登录',
    onSuccess: (result) => {
        console.log('登录成功!');
        window.location.href = result.redirectUrl;
    },
    onError: (error) => {
        console.error('登录失败:', error.message);
    }
});
```

### 3. 自动化脚本使用

```javascript
import miitLoginHelper from '@/utils/miitLoginHelper';

const result = await miitLoginHelper.quickLogin({
    username: '你的用户名',
    password: '你的密码'
});

if (result.success) {
    // 短信验证码已发送，等待用户输入
    const smsCode = await getUserInput('请输入短信验证码:');
    const loginResult = await result.loginFunction(smsCode);
}
```

## API 文档

### MiitLoginApi 类

#### 构造函数
```javascript
const loginApi = new MiitLoginApi();
```

#### 方法

##### getSmsCode(params)
获取短信验证码

**参数:**
- `params.username` (string): 用户名/统一社会信用代码/身份证号
- `params.password` (string): 密码
- `params.logintype` (string, 可选): 登录类型，默认 'sendMessage'

**返回值:**
```javascript
{
    success: boolean,
    message: string,
    data?: object
}
```

##### login(params)
执行登录

**参数:**
- `params.username` (string): 用户名
- `params.password` (string): 密码
- `params.smsCode` (string): 短信验证码
- `params.toUrl` (string, 可选): 登录成功后跳转URL
- `params.logintype` (string, 可选): 登录类型
- `params.dialogFlag` (string, 可选): 对话框标志

**返回值:**
```javascript
{
    success: boolean,
    message: string,
    redirectUrl?: string,
    needCompleteInfo?: boolean,
    needAgreeProtocol?: boolean,
    needFinishRegister?: boolean,
    needResetPassword?: boolean,
    data: object
}
```

##### loginFlow(credentials)
完整登录流程封装

**参数:**
- `credentials.username` (string): 用户名
- `credentials.password` (string): 密码
- `credentials.toUrl` (string, 可选): 目标URL

**返回值:**
```javascript
{
    success: boolean,
    step: string,
    message: string,
    loginFunction?: Function
}
```

### MiitLoginHelper 类

#### showLoginDialog(options)
显示登录对话框

**参数:**
- `options.title` (string, 可选): 对话框标题
- `options.toUrl` (string, 可选): 登录成功后跳转URL
- `options.onSuccess` (Function, 可选): 登录成功回调
- `options.onError` (Function, 可选): 登录失败回调

#### quickLogin(credentials)
快速登录（自动化脚本使用）

**参数:**
- `credentials.username` (string): 用户名
- `credentials.password` (string): 密码
- `credentials.toUrl` (string, 可选): 目标URL

## 登录结果处理

系统可能返回以下几种登录结果：

### 1. 登录成功 (result = 0)
```javascript
{
    success: true,
    message: '登录成功',
    redirectUrl: 'http://target-url.com'
}
```

### 2. 需要完善企业信息 (result = 2)
```javascript
{
    success: false,
    needCompleteInfo: true,
    message: '企业需完善企业通用基础信息表',
    completeInfoUrl: 'https://ythzxfw.miit.gov.cn/userCenter',
    redirectUrl: 'http://target-url.com'
}
```

### 3. 需要同意协议 (result = 3)
```javascript
{
    success: false,
    needAgreeProtocol: true,
    message: '需要同意用户协议',
    userId: 'user-id'
}
```

### 4. 需要完成注册 (result = 4)
```javascript
{
    success: false,
    needFinishRegister: true,
    message: '需要完成注册流程',
    userId: 'user-id'
}
```

### 5. 需要重置密码 (result = 10)
```javascript
{
    success: false,
    needResetPassword: true,
    message: '需要重置密码',
    resetPasswordUrl: '/retrieve_password.jsp?toUrl=...'
}
```

## 错误处理

```javascript
try {
    const result = await loginApi.login(params);
    
    if (result.success) {
        // 登录成功
        window.location.href = result.redirectUrl;
    } else {
        // 根据不同的错误类型进行处理
        if (result.needCompleteInfo) {
            if (confirm('需要完善企业信息，是否前往？')) {
                window.open(result.completeInfoUrl);
            }
        } else if (result.needResetPassword) {
            if (confirm('需要重置密码，是否前往？')) {
                window.location.href = result.resetPasswordUrl;
            }
        } else {
            alert(result.message);
        }
    }
} catch (error) {
    console.error('登录过程发生错误:', error);
    alert('登录失败，请稍后重试');
}
```

## 在Vue组件中使用

```vue
<template>
  <div>
    <el-form :model="loginForm">
      <el-form-item label="用户名">
        <el-input v-model="loginForm.username" placeholder="登录名/统一社会信用代码/身份证号"></el-input>
      </el-form-item>
      <el-form-item label="密码">
        <el-input type="password" v-model="loginForm.password" placeholder="请输入密码"></el-input>
      </el-form-item>
      <el-form-item label="验证码">
        <el-input v-model="loginForm.smsCode" placeholder="请输入短信验证码">
          <el-button slot="append" @click="getSmsCode" :loading="isGettingSms">获取验证码</el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="login" :loading="isLogging">登录</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import MiitLoginApi from '@/api/miitLoginApi';

export default {
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
        smsCode: ''
      },
      isGettingSms: false,
      isLogging: false,
      loginApi: new MiitLoginApi()
    };
  },
  methods: {
    async getSmsCode() {
      // 实现获取验证码逻辑
    },
    async login() {
      // 实现登录逻辑
    }
  }
};
</script>
```

## 依赖项

- axios: HTTP请求库
- crypto-js: 密码加密
- layer: 弹窗组件（可选，用于对话框功能）

## 注意事项

1. **密码安全**: 密码会使用MD5加密后传输
2. **跨域问题**: 可能需要配置代理或CORS
3. **验证码时效**: 短信验证码有时效性，请及时使用
4. **错误处理**: 建议根据不同的错误类型提供相应的用户指引
5. **登录状态**: 登录成功后会自动跳转到目标页面

## 更新日志

- v1.0.0: 初始版本，支持基础登录功能
- v1.1.0: 添加登录助手和对话框功能
- v1.2.0: 完善错误处理和状态管理
