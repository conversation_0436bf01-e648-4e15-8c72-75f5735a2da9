/*
 * @Description:
 * @Author: DreamAgo
 * @Date: 2024-02-22 16:29:05
 */
const CopyWebpackPlugin = require("copy-webpack-plugin");
const path = require("path");

// Generate pages object
const pagesObj = {};

const externals = {
    // vue: 'Vue',
    // 'element-ui': 'ELEMENT',
}

const plugins = []
plugins.push({
    from: path.resolve(`src/manifest.${process.env.VUE_APP_API_ENV}.json`),
    to: `${path.resolve("dist")}/manifest.json`
})
plugins.push({
    from: path.resolve("src/content/inject.js"),
    to: `${path.resolve("dist")}/js/inject.js`
})
plugins.push({
    from: path.resolve(`src/popup/index-${process.env.VUE_APP_API_ENV}.html`),
    to: `${path.resolve("dist")}/popup.html`
})
plugins.push({
    from: path.resolve("src/popup/index.js"),
    to: `${path.resolve("dist")}/js/popup.js`
})
plugins.push({
    from: path.resolve("src/options/index.html"),
    to: `${path.resolve("dist")}/options.html`
})
plugins.push({
    from: path.resolve("src/options/index.js"),
    to: `${path.resolve("dist")}/js/options.js`
})
plugins.push({
    from: path.resolve("src/background/index.js"),
    to: `${path.resolve("dist")}/js/background.js`
})

module.exports = {
    lintOnSave: false,
    devServer: {
        headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        },
        port: 8080, // 端口
        proxy: null
    },
    pages: pagesObj,
    configureWebpack: {
        entry: {
            'content': './src/content/index.js',
            'view': path.resolve("src/view/index.js")
        },
        output: {
            filename: 'js/[name].js',
            chunkFilename: 'js/[name].js'
        },
        plugins: [new CopyWebpackPlugin(plugins)], // 注意这里需要加 new
    },
    chainWebpack: config => {
        config.externals(externals)
    }
};
