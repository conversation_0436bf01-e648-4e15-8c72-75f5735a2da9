{"name": "chrom-jiaojiantong-vue3", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --mode development", "test": "vue-cli-service build --mode uat", "deploy": "vue-cli-service build --mode release", "lint": "vue-cli-service lint", "build-watch": "vue-cli-service build-watch --mode development"}, "dependencies": {"axios": "^1.10.0", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "element-ui": "^2.15.14", "jquery": "^3.7.1", "p-limit": "^3.0.0", "path-to-regexp": "^8.2.0", "postcss-custom-selectors": "^8.0.5", "vue": "2.6.14", "vue-template-compiler": "2.6.14"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^4.5.19", "copy-webpack-plugin": "^4.6.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^7.20.0", "postcss-prefix-selector": "^1.14.0", "vite-plugin-string": "^1.2.3", "vue-cli-plugin-chrome-ext": "~0.0.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue2-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}