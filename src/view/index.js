import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 引入不同页面对应的 Vue 组件
import MmatTransCreateApp from '@/components/MmatTransCreateApp.vue'
import DesignViewApp from '@/components/DesignViewApp.vue'
import ConstructionDesignReplyViewApp from '@/components/ConstructionDesignReplyViewApp.vue'
import DesignSubmitViewApp from '@/components/DesignSubmitViewApp.vue'
import AcceptReplyUpdateApp from "@/components/AcceptReplyUpdateApp.vue"
import ExplanationApp from '@/components/ExplanationApp.vue'
import AcceptComletedShowAddApp from "@/components/AcceptComletedShowAddApp.vue"
import AcceptReplyEditApp from "@/components/AcceptReplyEditApp.vue"
import MmatTransEditApp from "@/components/MmatTransEditApp.vue";
import AcceptComletedShowEditApp from "@/components/AcceptComletedShowEditApp.vue";
import MmatTransApproveApp from "@/components/MmatTransApproveApp.vue";
import transferCapitalPlatformApp from "@/components/transferCapitalPlatformApp.vue"
import cutoverDimensionApp from "@/components/cutoverDimensionApp.vue";

// 全局使用 ElementUI
Vue.use(ElementUI)

window.plugin = {
    log: function (...args) {
        console.groupCollapsed('%c[数智赋能平台审核工具]✔', "color: green; font-weight: bold;", ...args)
        console.trace()
        console.groupEnd()
    },
    error: function (...args) {
        console.groupCollapsed('%c[数智赋能平台审核工具]✖', "color: red; font-weight: bold;", ...args)
        console.trace()
        console.groupEnd()
    },
    warn: function (...args) {
        console.groupCollapsed('%c[数智赋能平台审核工具]✖', "color: red; font-weight: bold;", ...args)
        console.trace()
        console.groupEnd()
    },
}

plugin.log('=====> 开始加载审单插件')

const pageComponents = [
    {
        /**
         * 菜单：项目设计
         */
        path: '/projectDesign/designInvestigate/designInvestigateView',
        component: DesignViewApp,
    },
    {
        /**
         * 调拨申请单
         * 菜单路径：物资管理->项目调拨申请新->增项目调拨申请单
         */
        path: '/mmat/trans/create',
        component: MmatTransCreateApp,
    },
    {
        /**
         * 调拨申请单
         * 菜单路径：物资管理->项目调拨申请新->编辑项目调拨申请单
         */
        path: '/mmat/trans/edit/',
        component: MmatTransEditApp,
    },
    {
        /**
         * 调拨申请单
         * 菜单路径：物资管理->项目调拨申请新->审批项目调拨申请单
         */
        path: '/mmat/trans/approve/',
        component: MmatTransApproveApp,
    },

    {
        /**
         * 省公司竣工验收批复
         * 菜单路径：项目管理验收管理->竣工验收竣->工验收新增
         */
        path: '/projectAcceptance/acceptReplyUpdate/0/add/0/0',
        component: AcceptReplyUpdateApp,
    },
    {
        /**
         * 省公司竣工验收批复（编辑页面）
         * 路径示例：项目管理—>验收管理—>竣工验收批复—修改
         */
        path: '/projectAcceptance/acceptReplyUpdate/*/edit',
        component: AcceptReplyEditApp,
        match: (hash) => hash.includes('/projectAcceptance/acceptReplyUpdate/') && hash.includes('/edit'),
    },
    {
        /**
         * 地市竣工验收（新增页面）
         * 菜单路径：项目管理验收管理->竣工验收竣->工验收新增
         */
        path: '/projectAcceptance/acceptComletedShow/add/0/0/0',
        component: AcceptComletedShowAddApp,
    },
    {
        /**
         * 地市竣工验收(编辑页面)
         * 菜单路径：项目管理验收管理->竣工验收竣->工验收编辑
         */
        path: '/projectAcceptance/acceptComletedShow/edit',
        component: AcceptComletedShowEditApp,
    },
    {
        /**
         * 菜单：设计制作
         */
        path: '/projectDesign/designSubmit/designSubmitView',
        component: DesignSubmitViewApp,
    },
    {
        /**
         * 菜单：设计批复
         */
        path: '/projectDesign/designApproval/designApprovalView',
        component: ConstructionDesignReplyViewApp,
    },
    {
        /**
         * 菜单：设计交底
         */
        path: '/projectImplementation/explanation/show',
        component: ExplanationApp,
    },
    {
        /**
         * 转资校验
         * 菜单路径：项目管理->工程管理->转资工作台（卡控点：传送EAM）
         */
        path: '/transferringManage/transferCapitalPlatform',
        component: transferCapitalPlatformApp,
    },
    {
        /**
         * 菜单：项目管理->工程实施管理->割接上线交维->新增（卡控点：提交）
         */
        path: '/projectImplementation/cutoverDimension/add/',
        component: cutoverDimensionApp,
    },
    {
        /**
         * 菜单：项目管理->工程实施管理->割接上线交维->新增（卡控点：提交）
         */
        path: '/projectImplementation/cutoverDimension/edit/',
        component: cutoverDimensionApp,
    }
]

window.addEventListener('load', () => {
    // 获取或创建挂载容器
    let appContainer = document.getElementById('app-container')
    if (!appContainer) {
        appContainer = document.createElement('div')
        appContainer.id = 'app-container'
        document.body.appendChild(appContainer)
    }

    for (const page of pageComponents) {
        const hash = window.location.hash
        let isMatch = false

        // 处理通配符路径
        if (page.path.includes('*')) {
            const regex = new RegExp(page.path.replace('*', '.*'))
            isMatch = regex.test(hash)
        }
        // 处理有自定义匹配函数的情况
        else if (page.match) {
            isMatch = page.match(hash)
        }
        // 普通路径匹配
        else {
            isMatch = hash.includes(page.path)
        }

        if (isMatch) {
            new Vue({
                el: appContainer,
                render: h => h(page.component)
            })
             return // 匹配成功后退出循环
        }
    }

    plugin.log('=====> 没有匹配到任何路径')
})
