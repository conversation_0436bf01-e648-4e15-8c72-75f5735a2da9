import { Loading, Message } from 'element-ui';

const fn = (options) => {
    if(typeof options === 'string') {
        options = {message: options}
    }
    if (options && options.message) {
        options.message = `数智赋能平台自动化工具提示: ${options.message}`;
    }
    return options
}

export const EamMessage = {
    info(options) {Message.info(fn(options))},
    success(options) {Message.success(fn(options))},
    warning(options) {Message.warning(fn(options))},
    error(options) {Message.error(fn(options))},
};

const cache = {
    counter: 0,
    ins: null
};
window.cache= cache;

let lastCallTime = 0;
const MIN_INTERVAL = 200; // 防止 200ms 内重复打开
export const EamLoading = {
    ins: null,
    service(msg = '') {
        const now = Date.now();
        if (now - lastCallTime < MIN_INTERVAL) {
            return;
        }
        lastCallTime = now;

        const defaultText = '校验中，请勿关闭界面';
        const text = msg ? `${msg}` : defaultText;

        if (this.ins) {
            this.close();
        }

        this.ins = Loading.service({
            lock: true,
            fullscreen: true,
            text: text,
            customClass: 'eams-chrome-ext-loading',
            background: 'rgba(89,89,89,0.8)',
            zIndex: 9999999999
        });
    },
    close() {
        if(this.ins) {
            this.ins.close()
        }
    }
};
