export default {
    data() {
        return {
            $_multiSubmitStates: [], // 存储每个按钮的状态
            $_submitOptionsList: [], // 存储每个按钮的配置
            $_submitCleanup: null,   // 清理函数
        }
    },
    methods: {
        /**
         * 初始化多个提交按钮监听器
         * @param {Object|Array} options - 单个或多个按钮配置
         */
        initSubmitListener(options = {}) {
            // 兼容单对象写法
            const configs = Array.isArray(options) ? options : [options];

            // 清理旧的
            this.unbindAllSubmitListeners();

            this.$_multiSubmitStates = [];
            this.$_submitOptionsList = [];

            configs.forEach((cfg, idx) => {
                // 每个按钮都需要唯一key
                const key = cfg.key || `btn_${idx}`;
                // 合并默认配置
                const config = {
                    containerSelector: [
                        '.operation-content',
                        '.contents',
                        '.operation-content-header .contents'
                    ],
                    polling: true,
                    buttonText: '提交',
                    debug: true,
                    ...cfg,
                    key,
                };
                // 统一为数组
                if (typeof config.containerSelector === 'string') {
                    config.containerSelector = [config.containerSelector];
                }
                // 设置默认查找/提交方法
                if (!config.findButtonMethod) config.findButtonMethod = this.defaultFindButton;
                if (!config.submitHandler) config.submitHandler = this.defaultSubmitHandler;
                // 存储配置
                this.$_submitOptionsList.push(config);
                // 绑定
                this.bindSubmitListenerForKey(key, config);
                // 初始化 observer
                this.initMutationObserverForKey(key, config);
            });

            // 清理逻辑
            if (!this.$_submitCleanup) {
                this.$_submitCleanup = () => {
                    this.unbindAllSubmitListeners();
                };
                this.$once('hook:beforeDestroy', this.$_submitCleanup);
            }
        },

        /**
         * 绑定指定key的按钮
         */
        bindSubmitListenerForKey(key, config) {
            // 查找按钮
            const submitButton = config.findButtonMethod.call(this, config);
            if (!submitButton) {
                if (config.debug) plugin.warn(`[${key}] 未找到提交按钮`);
                // 轮询重试
                if (config.polling) {
                    setTimeout(() => this.bindSubmitListenerForKey(key, config), 1000);
                }
                return;
            }

            // 检查是否已绑定
            const state = this.$_multiSubmitStates.find(s => s.key === key);
            if (state && state.boundButton === submitButton && state.buttonBound) {
                if (config.debug) plugin.log(`[${key}] 按钮已绑定`);
                return;
            }

            // 解绑旧的
            this.unbindSubmitListenerForKey(key);

            // 包装handler，保证唯一
            const handler = async (event) => {
                await config.submitHandler.call(this, event, config, key);
            };

            // 绑定事件
            submitButton.addEventListener('click', handler, { capture: true });

            // 记录状态
            this.$_multiSubmitStates.push({
                key,
                boundButton: submitButton,
                buttonBound: true,
                handler,
                mutationObserver: null,
                buttonObserver: null,
            });

            // 按钮移除监听
            this.setupButtonObserverForKey(key, submitButton, config);

            if (config.debug) plugin.log(`[${key}] 按钮事件绑定成功`);
        },

        /**
         * 解绑指定key的按钮
         */
        unbindSubmitListenerForKey(key) {
            const idx = this.$_multiSubmitStates.findIndex(s => s.key === key);
            if (idx === -1) return;
            const state = this.$_multiSubmitStates[idx];
            const config = this.$_submitOptionsList.find(c => c.key === key);

            if (state.boundButton && state.handler) {
                state.boundButton.removeEventListener('click', state.handler, { capture: true });
            }
            if (state.buttonObserver) {
                state.buttonObserver.disconnect();
            }
            if (state.mutationObserver) {
                state.mutationObserver.disconnect();
            }
            this.$_multiSubmitStates.splice(idx, 1);

            if (config && config.debug) plugin.log(`[${key}] 按钮事件已解绑`);
        },

        /**
         * 解绑所有按钮
         */
        unbindAllSubmitListeners() {
            if (this.$_multiSubmitStates && this.$_multiSubmitStates.length) {
                this.$_multiSubmitStates.forEach(state => this.unbindSubmitListenerForKey(state.key));
            }
            this.$_multiSubmitStates = [];
            this.$_submitOptionsList = [];
        },

        /**
         * MutationObserver 监听指定key的容器
         */
        initMutationObserverForKey(key, config) {
            this.unbindMutationObserverForKey(key);

            const observer = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) {
                            // 判断是否为目标容器
                            if (config.containerSelector.some(selector => node.matches(selector) || node.querySelector(selector))) {
                                if (config.debug) plugin.log(`[${key}] 检测到动态容器，尝试绑定按钮`);
                                this.$nextTick(() => {
                                    this.bindSubmitListenerForKey(key, config);
                                });
                            }
                        }
                    });
                });
            });
            observer.observe(document.body, { childList: true, subtree: true });

            const state = this.$_multiSubmitStates.find(s => s.key === key);
            if (state) state.mutationObserver = observer;
        },

        /**
         * 断开指定key的MutationObserver
         */
        unbindMutationObserverForKey(key) {
            const state = this.$_multiSubmitStates.find(s => s.key === key);
            if (state && state.mutationObserver) {
                state.mutationObserver.disconnect();
                state.mutationObserver = null;
            }
        },

        /**
         * 按钮被移除时自动解绑
         */
        setupButtonObserverForKey(key, button, config) {
            // 先断开旧的
            const state = this.$_multiSubmitStates.find(s => s.key === key);
            if (state && state.buttonObserver) {
                state.buttonObserver.disconnect();
                state.buttonObserver = null;
            }
            // 新建
            const observer = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    Array.from(mutation.removedNodes).forEach(node => {
                        if (node === button || (node.contains && node.contains(button))) {
                            this.unbindSubmitListenerForKey(key);
                            if (config.debug) plugin.log(`[${key}] 按钮被移除，已解绑`);
                        }
                    });
                });
            });
            if (button.parentNode) {
                observer.observe(button.parentNode, { childList: true });
            }
            if (state) state.buttonObserver = observer;
        },

        /**
         * 默认按钮查找方法（支持多种容器选择器）
         */
        defaultFindButton(config) {
            const { containerSelector, buttonText, debug, key } = config;
            let foundButton = null;
            for (const selector of containerSelector) {
                const container = document.querySelector(selector);
                if (!container) continue;
                const buttons = container.querySelectorAll('button');
                for (const btn of buttons) {
                    const textContent = this.getButtonText(btn);
                    if (textContent === buttonText || textContent.includes(buttonText)) {
                        foundButton = btn;
                        break;
                    }
                }
                if (foundButton) break;
            }
            if (debug) {
                if (foundButton) {
                    plugin.log(`[${key}] 找到按钮: "${this.getButtonText(foundButton)}"`);
                } else {
                    plugin.warn(`[${key}] 未找到提交按钮: ${containerSelector.join(', ')}`);
                }
            }
            return foundButton;
        },

        /**
         * 获取按钮文本
         */
        getButtonText(button) {
            const textElement = button.querySelector('span:not(:empty)');
            if (textElement) return textElement.textContent.trim();
            const divElement = button.querySelector('div:not(:empty)');
            if (divElement) return divElement.textContent.trim();
            return button.textContent.trim();
        },

        /**
         * 默认提交处理函数
         */
        async defaultSubmitHandler(event, config, key) {
            // 新增：如果是插件自己触发的 click，直接 return，防止递归
            if (event.__isHandledByPlugin) return;

            event.stopImmediatePropagation();
            event.preventDefault();
            event.__isHandledByPlugin = true;

            if (config.debug) plugin.log(`[${key}] 按钮被点击`);

            try {
                const validateFunc = config.validate || this.validate;
                const isValid = await validateFunc.call(this);
                if (isValid) {
                    if (config.debug) plugin.log(`[${key}] 校验通过`);
                    await this.triggerNativeSubmitForKey(key, config);
                } else if (config.debug) {
                    plugin.warn(`[${key}] 校验未通过`);
                }
            } catch (error) {
                plugin.error(`[${key}] 提交处理出错:`, error);
            }
        },

        /**
         * 触发原生提交逻辑（指定key）
         */
        async triggerNativeSubmitForKey(key, config) {
            const state = this.$_multiSubmitStates.find(s => s.key === key);
            if (!state || !state.boundButton) return;

            // 临时解绑
            state.boundButton.removeEventListener('click', state.handler, { capture: true });
            state.buttonBound = false;

            if (config.debug) plugin.log(`[${key}] 触发原生提交逻辑`);

            // 创建并触发点击事件，并加上特殊标记
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
            });
            // 标记为“已由插件触发”
            clickEvent.__isHandledByPlugin = true;
            state.boundButton.dispatchEvent(clickEvent);

            // 重新绑定
            setTimeout(() => {
                if (document.body.contains(state.boundButton)) {
                    state.boundButton.addEventListener('click', state.handler, { capture: true });
                    state.buttonBound = true;
                    if (config.debug) plugin.log(`[${key}] 提交事件已重新绑定`);
                } else if (config.debug) {
                    plugin.warn(`[${key}] 按钮已不存在，无法重新绑定`);
                }
            }, 100);
        },

        /**
         * 表单验证方法（默认）
         */
        async validate() {
            plugin.warn('validate() 方法未实现');
            return false;
        }
    }
}