function tryGetEle(fn, max = 15) {
    let resolve, reject
    const promise = new Promise((s, j) => {
        resolve = s
        reject = j
    })
    let count = 0
    const cb = () => {
        let ele = fn()
        if (ele && (!Array.isArray(ele) || ele.length > 0)) {
            resolve(ele)
        } else if (count >= max) {
            reject('未找到节点')
        } else {
            count++
            console.log(`未找到节点，第${count}次重试`)
            setTimeout(cb, 200)
        }
    }
    cb()
    return promise
}

export function getButtonByText(text) {
    return tryGetEle(() => [...document.querySelectorAll("button.el-button.el-button--primary")].filter(but => but.innerText === text))
        .then(ele => ele[0])
}

export function getButtonByTexts(texts) {
    const list = texts.map(text => getButtonByText(text))
    return Promise.all(list)
}

export function getFormItem(property) {
    return tryGetEle(() => document.querySelector('.el-form')?.__vnode.ctx.props.model)
        .then(model => model[property])
}

export function getTimeLine() {
    return tryGetEle(() => document.querySelector('.el-timeline-item__wrapper .el-timeline-item__content .card .line .lineContent'))
        .then(ele => ele.textContent)
}

export function getEleByClass(className) {
    if (className.charAt(0) !== '.') {
        className = '.' + className
    }
    return tryGetEle(() => document.querySelectorAll(className));
}

export function hideEle(ele) {
    if(ele) {
        ele.style.display = 'none'
        return true
    } else {
        return false
    }
}

export function hideEleList(eleList) {
    let flag = true
    eleList.forEach(ele => {
        flag = hideEle(ele) && flag
    })
    return flag
}