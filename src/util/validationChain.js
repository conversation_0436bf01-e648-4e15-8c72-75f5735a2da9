import { EamLoading } from '@/util/EamElementExt';

/**
 * 运行校验链
 * @param {Array} chain - 校验链配置数组
 * @param {Object} context - 上下文对象
 * @returns {Promise<boolean>} 校验结果
 */
export async function runValidationChain(chain, context = {}) {
  // 统一管理加载状态
  EamLoading.service();
  try {
    for (const item of chain) {
      if (!item.enable) {
        plugin.log(`【${item.name}】已禁用，跳过校验`);
        continue;
      }
      plugin.log(`开始执行【${item.name}】校验`);
      const result = await item.validate(context);
      if (!result) {
        if (item.skipOnFail) {
          plugin.warn(`【${item.name}】校验未通过，但已配置跳过`);
        } else {
          plugin.warn(`【${item.name}】校验未通过，终止校验链`);
          return false;
        }
      } else {
        plugin.log(`【${item.name}】校验通过`);
      }
    }
    plugin.log('所有校验项目执行完成');
    return true;
  } catch (error) {
    plugin.error('校验链执行过程中发生错误:', error);
    return false;
  } finally {
    // 确保加载状态被关闭
    EamLoading.close();
  }
}