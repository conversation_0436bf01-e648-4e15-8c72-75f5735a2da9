import { queryWhiteList } from '@/api/commonApi';
import {getInputValue, getLabelByText} from "@/validate/mmatTrans/utils/getCommonElementAcquisition";

export default async function validateProjectCode(context) {
  const label = getLabelByText('调入项目编码');
  if (!label) {
    plugin.warn('未找到调入项目编码字段');
    return false;
  }
  const input = getInputValue(label);
  context.projectCode = input;
  context.whiteList = await queryWhiteList('MMAT_TRANS_CREATE_APP', input);
  return !!input;
} 