import {
  getSelectedTableRows,
  getColumnIndex,
  getTransferInTaskCodeByApprove
} from '../utils/getCommonElementAcquisition';
import { getCompleteTheReportList } from '@/api/mmatTransCreateApp';
import { logValidation } from '@/api/commonApi';
import { EamLoading } from '@/util/EamElementExt';
import { Message } from 'element-ui';

export default async function validateTaskCodesForApprove(context) {
  if (context.allocationType !== '项目间调拨' || !context.projectCode) {
    plugin.warn('非项目间调拨或无项目编码，跳过');
    return true;
  }
  let errorCode = [];
  try {
    EamLoading.service();
    const columnIndex = getColumnIndex('调入任务编码');
    const selectedRows = getSelectedTableRows();
    const validatePromises = selectedRows.map(async row => {
      const [taskCode] = await Promise.all([getTransferInTaskCodeByApprove(row, columnIndex)]);
      if (!taskCode) return true;
      // 白名单校验
      if (Array.isArray(context.whiteList) && context.whiteList.some(item => item.taskCode && item.taskCode.includes(taskCode))) {
        plugin.warn(`[${taskCode}] 调入任务编码在白名单中，跳过`);
        return true;
      }
      // 完工状态校验
      const requestParams = {
        projectCode: context.projectCode,
        selectDefault: 'PIU_IT_TZJHGLY',
        sysDataAuthQueryDetailVo: { companyCode: [], secondOrgCode: [], key: 'PIU_IT_TZJHGLY', value: '' },
        checkedKey: [],
        secondOrgCode: [],
        size: 100,
        page: 1,
        taskcode: taskCode
      };
      let validationResult = false;
      let responseData = null;
      try {
        await getCompleteTheReportList(requestParams).then(orderData => {
          responseData = orderData;
          const { data } = orderData || {};
          const { body } = data || {};
          const { completionReportList } = body || {};
          const firstItem = Array.isArray(completionReportList) ? completionReportList[0] : null;
          if (firstItem && firstItem.documentStatus === '02') {
            errorCode.push(taskCode);
            validationResult = false;
          } else {
            validationResult = true;
          }
        });
      } catch (error) {
        errorCode.push(taskCode);
        validationResult = true;
        plugin.error(`任务编码 ${taskCode} 验证失败:`, error);
      } finally {
        await logValidation({
          businessType: 'MMAT_TRANS_CREATE_APP',
          taskCodes: taskCode,
          projectCode: context.projectCode,
          validationResults: validationResult,
          requestParams: JSON.stringify(requestParams),
          responseData: JSON.stringify(responseData),
          isSuccessful: validationResult,
          errorMessages: validationResult ? '' : `任务编码 ${taskCode} 已完工`,
          pageUrl: window.location.href
        });
      }
      return validationResult;
    });
    const results = await Promise.all(validatePromises);
    if (!results.every(valid => valid)) {
      Message.warning(errorCode.join(', ') + '调入任务编码已完工，请检查任务编码');
      return false;
    }
    return true;
  } finally {
    EamLoading.close();
  }
} 