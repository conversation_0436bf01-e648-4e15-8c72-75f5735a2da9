import {getLabelTextForApprove, getValueForApprove} from '../utils/getCommonElementAcquisition';
export default async function validateAllocationTypeForApprove(context) {
  const label = getLabelTextForApprove('调拨类型');
  if (!label) {
    plugin.warn('未找到调拨类型元素');
    return false;
  }
  const input = getValueForApprove(label);
  context.allocationType = input;
  return input === '项目间调拨' || input === '项目内调拨';
} 