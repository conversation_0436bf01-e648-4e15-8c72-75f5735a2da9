// 获取指定 label 的文本内容
export function getLabelText(selector) {
  const label = document.querySelector(selector);
  return label ? label.textContent.trim() : '';
}

// 根据 label 内容查找 label 元素
export function getLabelByText(text) {
  const labels = document.querySelectorAll('.el-form-item__label');
  for (const label of labels) {
    if (label.textContent.includes(text)) {
      return label;
    }
  }
  return null;
}

// 获取 label 关联的 input 值
export function getInputValue(label) {
  const input = label.closest('.el-form-item').querySelector('.el-input__inner');
  return input ? input.value : '';
}

// 获取表格所有选中行
export function getSelectedTableRows() {
  const rows = [];
  const table = document.querySelector('.el-table__body-wrapper .el-table__body');
  if (!table) return rows;
  const allRows = table.querySelectorAll('tr.el-table__row');
  allRows.forEach(row => rows.push(row));
  return rows;
}

// 获取表头指定列的索引
export function getColumnIndex(headerText) {
  const headerCells = document.querySelectorAll('.el-table__header .el-table__cell');
  let targetIndex = -1;
  headerCells.forEach((cell, index) => {
    const cellText = cell.querySelector('.cell')?.textContent?.trim();
    if (cellText && cellText.includes(headerText)) {
      targetIndex = index;
    }
  });
  return targetIndex;
}

// 获取行中指定列的任务编码
export function getTransferInTaskCode(row, columnIndex) {
  const cells = row.querySelectorAll('td');
  if (cells.length > columnIndex) {
    const input = cells[columnIndex].querySelector('.el-input__inner');
    return input ? input.title : null;
  }
  return null;
}

// 获取审批页面行中指定列的任务编码
export function getTransferInTaskCodeByApprove(row, columnIndex) {
  const cells = row.querySelectorAll('td');
  if (cells.length > columnIndex) {
    // 适配 approve 页面的结构
    const span = cells[columnIndex].querySelector('span.el-tooltip.item');
    if (span) {
      // 优先取 textContent，若为空可取 title 属性
      let taskCode = span.textContent.trim();
      if (!taskCode && span.title) {
        taskCode = span.title.trim();
      }
      return taskCode;
    }
  }
  return null;
}

// 获取审批页面label节点
export function getLabelTextForApprove(text) {
  const labels = document.querySelectorAll('.el-form-item__label');
  for (const label of labels) {
    const labelContent = label.querySelector('.order_form_label');
    if (labelContent && labelContent.textContent.trim().includes(text)) {
      return label;
    }
  }
  return null;
}

// 获取审批页面内容
export function getValueForApprove(label) {
  if (!label) return '';
  const elFormItem = label.parentNode;
  const contentDiv = elFormItem.querySelector('.order_form_content');
  return contentDiv ? contentDiv.textContent.trim() : '';
}



