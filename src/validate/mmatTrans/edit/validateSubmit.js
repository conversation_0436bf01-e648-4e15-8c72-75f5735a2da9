import validateAllocationType from '../business/validateAllocationType'
import validateProjectCode from '../business/validateProjectCode'
import validateTaskCodes from '../business/validateTaskCodes'
import { runValidationChain } from '@/util/validationChain'

export default async function validateSubmit() {
  // 可通过接口动态获取配置
  const configFromApi = {
    allocationTypeEnable: true,
    projectCodeEnable: true,
    taskCodesEnable: true,
    whiteListEnable: false,
    whiteListSkipOnFail: true
  };
  const context = {};
  const chain = [
    {
      name: '调拨类型校验',
      enable: configFromApi.allocationTypeEnable,
      skipOnFail: false,
      validate: validateAllocationType
    },
    {
      name: '项目编码校验',
      enable: configFromApi.projectCodeEnable,
      skipOnFail: false,
      validate: validateProjectCode
    },
    {
      name: '任务编码校验',
      enable: configFromApi.taskCodesEnable,
      skipOnFail: false,
      validate: validateTaskCodes
    }
  ];
  return await runValidation<PERSON>hai<PERSON>(chain, context);
} 