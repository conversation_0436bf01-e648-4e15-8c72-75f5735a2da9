
import validateTaskCodesForApprove from '../business/validateTaskCodesForApprove'
import { runValidationChain } from '@/util/validationChain'
import validateProjectCodeForApprove from "@/validate/mmatTrans/business/validateProjectCodeForApprove";
import validateAllocationTypeForApprove from "@/validate/mmatTrans/business/validateAllocationTypeForApprove";

export default async function validateConfirm(context) {
  // 可通过接口动态获取配置
  const configFromApi = {
    allocationTypeEnable: true,
    projectCodeEnable: true,
    taskCodesEnable: true,
    whiteListEnable: false,
    whiteListSkipOnFail: true
  };
  context = context || {};
  const chain = [
    {
      name: '调拨类型校验',
      enable: configFromApi.allocationTypeEnable,
      skipOnFail: false,
      validate: validateAllocationTypeForApprove
    },
    {
      name: '项目编码校验',
      enable: configFromApi.projectCodeEnable,
      skipOnFail: false,
      validate: validateProjectCodeForApprove
    },
    {
      name: '任务编码校验',
      enable: configFromApi.taskCodesEnable,
      skipOnFail: false,
      validate: validateTaskCodesForApprove
    }
  ];
  return await runValidationChain(chain, context);
} 