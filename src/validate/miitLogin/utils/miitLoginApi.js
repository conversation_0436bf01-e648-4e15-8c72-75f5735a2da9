import CryptoJS from 'crypto-js';

/**
 * 工信部统一身份认证系统登录封装
 * 网址: https://txjs.miit.gov.cn/sso.action (重定向到 http://ucenter.miit.gov.cn/login.jsp)
 * 使用原生fetch避免CORS预检请求问题
 */
class MiitLoginApi {
    constructor() {
        // 直接访问目标地址
        this.baseUrl = 'http://ucenter.miit.gov.cn';
        this.loginUrl = `${this.baseUrl}/login.action`;
        this.sendMessageUrl = `${this.baseUrl}/sendMessage.action`;

        console.log('🔌 工信部登录API已初始化 - 使用fetch避免CORS问题');
    }

    /**
     * MD5加密密码 (模拟页面中的md5函数)
     * @param {string} password - 原始密码
     * @returns {string} MD5加密后的密码
     */
    md5Encrypt(password) {
        return CryptoJS.MD5(password).toString();
    }

    /**
     * 测试连接到工信部服务器
     * @returns {Promise<Object>} 测试结果
     */
    async testConnection() {
        try {
            console.log('🧪 测试连接到工信部服务器...');

            const response = await fetch(`${this.baseUrl}/login.jsp`, {
                method: 'HEAD',
                mode: 'cors',
                credentials: 'include'
            });

            const result = {
                success: response.ok,
                status: response.status,
                statusText: response.statusText,
                message: response.ok ? '连接正常' : `连接失败: ${response.status} ${response.statusText}`
            };

            console.log('🧪 连接测试结果:', result);
            return result;

        } catch (error) {
            console.error('❌ 连接测试失败:', error);
            return {
                success: false,
                message: `连接失败: ${error.message}`,
                error: error
            };
        }
    }

    /**
     * 发送HTTP请求 - 使用fetch避免CORS预检请求
     * @param {string} url - 请求URL
     * @param {URLSearchParams} data - 表单数据
     * @returns {Promise<Object>} 响应结果
     */
    async sendRequest(url, data) {
        const fullUrl = this.baseUrl + url;

        try {
            console.log('🚀 发送请求:', fullUrl);

            // 使用fetch发送简单POST请求，避免预检请求
            const response = await fetch(fullUrl, {
                method: 'POST',
                headers: {
                    // 只使用简单头部，避免触发预检请求
                    'Content-Type': 'application/x-www-form-urlencoded'
                    // 不添加User-Agent等自定义头部，避免预检请求
                },
                body: data,
                // 浏览器插件环境可以跨域发送凭据
                credentials: 'include',
                // 跨域模式
                mode: 'cors'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 解析响应数据
            let responseData;
            const contentType = response.headers.get('content-type');

            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                // 尝试解析为JSON，如果失败则返回文本
                const text = await response.text();
                try {
                    responseData = JSON.parse(text);
                } catch (e) {
                    console.warn('响应不是有效的JSON，返回原始文本');
                    responseData = { message: text, rawResponse: text };
                }
            }

            console.log('✅ 请求成功:', response.status, responseData);
            return { data: responseData, status: response.status };

        } catch (error) {
            console.error('❌ 请求失败:', error);

            // 提供更详细的错误信息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络连接失败，请检查网络连接或目标服务器状态');
            }

            if (error.message.includes('CORS')) {
                throw new Error('跨域请求被阻止，请确保在浏览器插件环境中运行');
            }

            throw error;
        }
    }

    /**
     * 获取短信验证码
     * @param {Object} params - 参数对象
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @returns {Promise<Object>} 返回结果
     */
    async getSmsCode({ username, password, logintype = 'sendMessage' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号或密码不能为空！');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数
            const params = new URLSearchParams({
                logintype: logintype,
                username: username.trim(),
                password: encryptedPassword
            });

            console.log('发送短信验证码请求:', { username, logintype });

            // 使用多策略请求方法
            const response = await this.sendRequest('/sendMessage.action', params);

            if (response.data.result === 0) {
                return {
                    success: true,
                    message: `已发送到手机号：${response.data.msg}，请注意查收`,
                    data: response.data
                };
            } else if (response.data.result === 999) {
                throw new Error('短信通道繁忙，请稍后再试(101)！');
            } else {
                throw new Error(response.data.msg || '发送短信验证码失败');
            }
        } catch (error) {
            console.error('获取短信验证码失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 执行登录
     * @param {Object} params - 登录参数
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} params.smsCode - 短信验证码
     * @param {string} [params.toUrl] - 登录成功后跳转的URL
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @param {string} [params.dialogFlag=''] - 对话框标志
     * @returns {Promise<Object>} 登录结果
     */
    async login({ username, password, smsCode, toUrl, logintype = 'sendMessage', dialogFlag = '' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号和密码不能为空');
            }
            if (!smsCode) {
                throw new Error('短信验证码不能为空');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数 (模拟表单提交)
            const formData = new URLSearchParams({
                username: username.trim(),
                password: encryptedPassword,  // 这里是加密后的密码，对应隐藏字段#pwd
                yznum: smsCode.trim(),
                logintype: logintype,
                toUrl: toUrl || 'http%3A%2F%2Ftxjs.miit.gov.cn',
                flag: '',
                mobile: '',
                getyznum: '',
                dialogFlag: dialogFlag
            });

            console.log('执行登录请求:', { username, logintype, toUrl });

            // 使用多策略请求方法
            const response = await this.sendRequest('/login.action', formData);

            return this.handleLoginResponse(response.data);
            
        } catch (error) {
            console.error('登录失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 处理登录响应结果
     * @param {Object} data - 服务器响应数据
     * @returns {Object} 处理后的结果
     */
    handleLoginResponse(data) {
        switch (data.result) {
            case 0:
                // 登录成功
                return {
                    success: true,
                    message: '登录成功',
                    redirectUrl: data.msg,
                    data: data
                };
            case 2:
                // 需要完善企业信息
                return {
                    success: false,
                    needCompleteInfo: true,
                    message: '企业需完善企业通用基础信息表',
                    completeInfoUrl: 'https://ythzxfw.miit.gov.cn/userCenter',
                    redirectUrl: data.msg,
                    data: data
                };
            case 3:
                // 需要同意协议
                return {
                    success: false,
                    needAgreeProtocol: true,
                    message: '需要同意用户协议',
                    userId: data.msg,
                    data: data
                };
            case 4:
                // 需要完成注册
                return {
                    success: false,
                    needFinishRegister: true,
                    message: '需要完成注册流程',
                    userId: data.msg,
                    data: data
                };
            case 10:
                // 需要重置密码
                return {
                    success: false,
                    needResetPassword: true,
                    message: data.msg,
                    resetPasswordUrl: '/retrieve_password.jsp?toUrl=http%3A%2F%2Ftxjs.miit.gov.cn',
                    data: data
                };
            default:
                // 其他错误
                return {
                    success: false,
                    message: data.msg || '登录失败',
                    data: data
                };
        }
    }

    /**
     * 获取登录页面信息
     * @param {string} [toUrl] - 目标URL
     * @returns {Promise<Object>} 页面信息
     */
    async getLoginPageInfo(toUrl = 'http://txjs.miit.gov.cn') {
        try {
            const response = await this.client.get(`/login.jsp?toUrl=${encodeURIComponent(toUrl)}`);
            
            return {
                success: true,
                message: '获取登录页面信息成功',
                data: {
                    loginUrl: this.loginUrl,
                    sendMessageUrl: this.sendMessageUrl,
                    toUrl: toUrl
                }
            };
        } catch (error) {
            console.error('获取登录页面信息失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 完整的登录流程封装
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.username - 用户名
     * @param {string} credentials.password - 密码
     * @param {string} [credentials.toUrl] - 目标URL
     * @returns {Promise<Object>} 登录流程结果
     */
    async loginFlow({ username, password, toUrl }) {
        try {
            console.log('开始登录流程...');
            
            // 1. 获取短信验证码
            console.log('步骤1: 获取短信验证码');
            const smsResult = await this.getSmsCode({ username, password });
            
            if (!smsResult.success) {
                return {
                    success: false,
                    step: 'getSmsCode',
                    message: smsResult.message,
                    error: smsResult.error
                };
            }

            return {
                success: true,
                step: 'getSmsCode',
                message: smsResult.message,
                nextStep: 'waitForSmsCode',
                loginFunction: (smsCode) => this.login({ username, password, smsCode, toUrl })
            };
            
        } catch (error) {
            console.error('登录流程失败:', error);
            return {
                success: false,
                step: 'loginFlow',
                message: error.message,
                error: error
            };
        }
    }
    /**
     * 快速测试API功能
     * @param {Object} testCredentials - 测试凭据
     * @returns {Promise<Object>} 测试结果
     */
    async quickTest(testCredentials = { username: 'test', password: 'test' }) {
        console.log('🧪 开始API快速测试...');

        const results = {
            connection: null,
            smsApi: null,
            environment: {
                isExtension: typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id,
                origin: window.location.origin,
                userAgent: navigator.userAgent.substring(0, 100) + '...'
            }
        };

        try {
            // 1. 测试连接
            console.log('步骤1: 测试连接');
            results.connection = await this.testConnection();

            // 2. 测试短信API（使用测试数据，不会真实发送）
            console.log('步骤2: 测试短信API');
            results.smsApi = await this.getSmsCode(testCredentials);

            console.log('🧪 API测试完成:', results);
            return {
                success: true,
                message: 'API测试完成',
                results: results
            };

        } catch (error) {
            console.error('❌ API测试失败:', error);
            return {
                success: false,
                message: `API测试失败: ${error.message}`,
                error: error,
                results: results
            };
        }
    }
}

// 创建全局实例供测试使用
if (typeof window !== 'undefined') {
    window.MiitLoginApi = MiitLoginApi;

    // 提供全局测试函数
    window.testMiitLoginApi = async () => {
        const api = new MiitLoginApi();
        return await api.quickTest();
    };

    console.log('🔌 工信部登录API已加载，可以使用 window.testMiitLoginApi() 进行测试');
}

export default MiitLoginApi;
