import axios from 'axios';
import CryptoJS from 'crypto-js';

/**
 * 工信部统一身份认证系统登录封装
 * 网址: https://txjs.miit.gov.cn/sso.action (重定向到 http://ucenter.miit.gov.cn/login.jsp)
 */
class MiitLoginApi {
    constructor() {
        // 浏览器插件直接访问目标地址，不需要代理
        this.baseUrl = 'http://ucenter.miit.gov.cn';
        this.loginUrl = `${this.baseUrl}/login.action`;
        this.sendMessageUrl = `${this.baseUrl}/sendMessage.action`;

        // 浏览器插件环境配置 - 避免触发预检请求
        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: 50000,
            // 浏览器插件可以跨域发送凭据
            withCredentials: true,
            headers: {
                // 使用简单的Content-Type，避免预检请求
                'Content-Type': 'application/x-www-form-urlencoded',
                // 移除会触发预检请求的自定义头部
                'Accept': '*/*',
                // 添加必要的浏览器头部
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });

        // 添加请求拦截器
        this.client.interceptors.request.use(
            config => {
                console.log('发送请求:', config.method.toUpperCase(), config.url);
                return config;
            },
            error => {
                console.error('请求错误:', error);
                return Promise.reject(error);
            }
        );

        // 添加响应拦截器
        this.client.interceptors.response.use(
            response => {
                console.log('收到响应:', response.status, response.data);
                return response;
            },
            error => {
                console.error('响应错误:', error);
                if (error.code === 'ERR_NETWORK') {
                    throw new Error('网络连接失败，请检查网络或代理配置');
                }
                return Promise.reject(error);
            }
        );
    }

    /**
     * MD5加密密码 (模拟页面中的md5函数)
     * @param {string} password - 原始密码
     * @returns {string} MD5加密后的密码
     */
    md5Encrypt(password) {
        return CryptoJS.MD5(password).toString();
    }

    /**
     * 发送请求（浏览器插件专用）
     * @param {string} url - 请求URL
     * @param {URLSearchParams} data - 表单数据
     * @returns {Promise<Object>} 响应结果
     */
    async sendRequest(url, data) {
        try {
            console.log('🔌 浏览器插件发送请求:', url);

            // 浏览器插件可以直接跨域请求，使用axios
            const response = await this.client.post(url, data, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                withCredentials: true // 插件环境可以发送凭据
            });

            console.log('✅ 请求成功:', response.status, response.data);
            return response;

        } catch (error) {
            console.error('❌ 请求失败:', error);
            return this.fallbackFetch(url, data);
        }
    }

    /**
     * fetch降级方案（浏览器插件环境）
     * @param {string} url - 请求URL
     * @param {URLSearchParams} data - 表单数据
     * @returns {Promise<Object>} 响应结果
     */
    async fallbackFetch(url, data) {
        const fullUrl = this.baseUrl + url;

        console.log('🔄 使用fetch发送请求:', fullUrl);

        const response = await fetch(fullUrl, {
            method: 'POST',
            body: data,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            credentials: 'include', // 插件环境包含凭据
            mode: 'cors'
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json();
        console.log('✅ Fetch请求成功:', response.status, responseData);

        return { data: responseData };
    }

    /**
     * 获取短信验证码
     * @param {Object} params - 参数对象
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @returns {Promise<Object>} 返回结果
     */
    async getSmsCode({ username, password, logintype = 'sendMessage' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号或密码不能为空！');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数
            const params = new URLSearchParams({
                logintype: logintype,
                username: username.trim(),
                password: encryptedPassword
            });

            console.log('发送短信验证码请求:', { username, logintype });

            // 使用多策略请求方法
            const response = await this.sendRequest('/sendMessage.action', params);

            if (response.data.result === 0) {
                return {
                    success: true,
                    message: `已发送到手机号：${response.data.msg}，请注意查收`,
                    data: response.data
                };
            } else if (response.data.result === 999) {
                throw new Error('短信通道繁忙，请稍后再试(101)！');
            } else {
                throw new Error(response.data.msg || '发送短信验证码失败');
            }
        } catch (error) {
            console.error('获取短信验证码失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 执行登录
     * @param {Object} params - 登录参数
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} params.smsCode - 短信验证码
     * @param {string} [params.toUrl] - 登录成功后跳转的URL
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @param {string} [params.dialogFlag=''] - 对话框标志
     * @returns {Promise<Object>} 登录结果
     */
    async login({ username, password, smsCode, toUrl, logintype = 'sendMessage', dialogFlag = '' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号和密码不能为空');
            }
            if (!smsCode) {
                throw new Error('短信验证码不能为空');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数 (模拟表单提交)
            const formData = new URLSearchParams({
                username: username.trim(),
                password: encryptedPassword,  // 这里是加密后的密码，对应隐藏字段#pwd
                yznum: smsCode.trim(),
                logintype: logintype,
                toUrl: toUrl || 'http%3A%2F%2Ftxjs.miit.gov.cn',
                flag: '',
                mobile: '',
                getyznum: '',
                dialogFlag: dialogFlag
            });

            console.log('执行登录请求:', { username, logintype, toUrl });

            // 使用多策略请求方法
            const response = await this.sendRequest('/login.action', formData);

            return this.handleLoginResponse(response.data);
            
        } catch (error) {
            console.error('登录失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 处理登录响应结果
     * @param {Object} data - 服务器响应数据
     * @returns {Object} 处理后的结果
     */
    handleLoginResponse(data) {
        switch (data.result) {
            case 0:
                // 登录成功
                return {
                    success: true,
                    message: '登录成功',
                    redirectUrl: data.msg,
                    data: data
                };
            case 2:
                // 需要完善企业信息
                return {
                    success: false,
                    needCompleteInfo: true,
                    message: '企业需完善企业通用基础信息表',
                    completeInfoUrl: 'https://ythzxfw.miit.gov.cn/userCenter',
                    redirectUrl: data.msg,
                    data: data
                };
            case 3:
                // 需要同意协议
                return {
                    success: false,
                    needAgreeProtocol: true,
                    message: '需要同意用户协议',
                    userId: data.msg,
                    data: data
                };
            case 4:
                // 需要完成注册
                return {
                    success: false,
                    needFinishRegister: true,
                    message: '需要完成注册流程',
                    userId: data.msg,
                    data: data
                };
            case 10:
                // 需要重置密码
                return {
                    success: false,
                    needResetPassword: true,
                    message: data.msg,
                    resetPasswordUrl: '/retrieve_password.jsp?toUrl=http%3A%2F%2Ftxjs.miit.gov.cn',
                    data: data
                };
            default:
                // 其他错误
                return {
                    success: false,
                    message: data.msg || '登录失败',
                    data: data
                };
        }
    }

    /**
     * 获取登录页面信息
     * @param {string} [toUrl] - 目标URL
     * @returns {Promise<Object>} 页面信息
     */
    async getLoginPageInfo(toUrl = 'http://txjs.miit.gov.cn') {
        try {
            const response = await this.client.get(`/login.jsp?toUrl=${encodeURIComponent(toUrl)}`);
            
            return {
                success: true,
                message: '获取登录页面信息成功',
                data: {
                    loginUrl: this.loginUrl,
                    sendMessageUrl: this.sendMessageUrl,
                    toUrl: toUrl
                }
            };
        } catch (error) {
            console.error('获取登录页面信息失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 完整的登录流程封装
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.username - 用户名
     * @param {string} credentials.password - 密码
     * @param {string} [credentials.toUrl] - 目标URL
     * @returns {Promise<Object>} 登录流程结果
     */
    async loginFlow({ username, password, toUrl }) {
        try {
            console.log('开始登录流程...');
            
            // 1. 获取短信验证码
            console.log('步骤1: 获取短信验证码');
            const smsResult = await this.getSmsCode({ username, password });
            
            if (!smsResult.success) {
                return {
                    success: false,
                    step: 'getSmsCode',
                    message: smsResult.message,
                    error: smsResult.error
                };
            }

            return {
                success: true,
                step: 'getSmsCode',
                message: smsResult.message,
                nextStep: 'waitForSmsCode',
                loginFunction: (smsCode) => this.login({ username, password, smsCode, toUrl })
            };
            
        } catch (error) {
            console.error('登录流程失败:', error);
            return {
                success: false,
                step: 'loginFlow',
                message: error.message,
                error: error
            };
        }
    }
}

export default MiitLoginApi;
