/**
 * 工信部登录API - 浏览器插件专用版本
 * 直接访问目标地址，无需代理
 */
import CryptoJS from 'crypto-js';

class MiitLoginApiExtension {
    constructor() {
        // 直接访问目标地址
        this.baseUrl = 'http://ucenter.miit.gov.cn';
        this.loginUrl = `${this.baseUrl}/login.action`;
        this.sendMessageUrl = `${this.baseUrl}/sendMessage.action`;
        
        console.log('🔌 工信部登录API - 浏览器插件版本已初始化');
    }

    /**
     * MD5加密密码
     * @param {string} password - 原始密码
     * @returns {string} MD5加密后的密码
     */
    md5Encrypt(password) {
        return CryptoJS.MD5(password).toString();
    }

    /**
     * 发送HTTP请求（浏览器插件专用）
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 响应结果
     */
    async sendRequest(url, data, options = {}) {
        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': '*/*',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                ...options.headers
            },
            body: data instanceof URLSearchParams ? data : new URLSearchParams(data),
            credentials: 'include', // 浏览器插件可以发送凭据
            mode: 'cors',
            ...options
        };

        console.log('🚀 发送请求:', url, requestOptions);

        try {
            const response = await fetch(url, requestOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 尝试解析JSON响应
            let responseData;
            const contentType = response.headers.get('content-type');
            
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                const text = await response.text();
                try {
                    responseData = JSON.parse(text);
                } catch (e) {
                    responseData = { message: text };
                }
            }

            console.log('✅ 请求成功:', response.status, responseData);
            return { data: responseData, status: response.status };

        } catch (error) {
            console.error('❌ 请求失败:', error);
            throw error;
        }
    }

    /**
     * 获取短信验证码
     * @param {Object} params - 参数对象
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @returns {Promise<Object>} 返回结果
     */
    async getSmsCode({ username, password, logintype = 'sendMessage' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号或密码不能为空！');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数
            const params = new URLSearchParams({
                logintype: logintype,
                username: username.trim(),
                password: encryptedPassword
            });

            console.log('📱 发送短信验证码请求:', { username, logintype });

            const response = await this.sendRequest(this.sendMessageUrl, params);
            
            if (response.data.result === 0) {
                return {
                    success: true,
                    message: `已发送到手机号：${response.data.msg}，请注意查收`,
                    data: response.data
                };
            } else if (response.data.result === 999) {
                throw new Error('短信通道繁忙，请稍后再试(101)！');
            } else {
                throw new Error(response.data.msg || '发送短信验证码失败');
            }
        } catch (error) {
            console.error('获取短信验证码失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 执行登录
     * @param {Object} params - 登录参数
     * @param {string} params.username - 用户名
     * @param {string} params.password - 密码
     * @param {string} params.smsCode - 短信验证码
     * @param {string} [params.toUrl] - 登录成功后跳转的URL
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @param {string} [params.dialogFlag=''] - 对话框标志
     * @returns {Promise<Object>} 登录结果
     */
    async login({ username, password, smsCode, toUrl, logintype = 'sendMessage', dialogFlag = '' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号和密码不能为空');
            }
            if (!smsCode) {
                throw new Error('短信验证码不能为空');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数 (模拟表单提交)
            const formData = new URLSearchParams({
                username: username.trim(),
                password: encryptedPassword,  // 这里是加密后的密码，对应隐藏字段#pwd
                yznum: smsCode.trim(),
                logintype: logintype,
                toUrl: toUrl || 'http%3A%2F%2Ftxjs.miit.gov.cn',
                flag: '',
                mobile: '',
                getyznum: '',
                dialogFlag: dialogFlag
            });

            console.log('🔐 执行登录请求:', { username, logintype, toUrl });

            const response = await this.sendRequest(this.loginUrl, formData);
            
            return this.handleLoginResponse(response.data);
            
        } catch (error) {
            console.error('登录失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 处理登录响应结果
     * @param {Object} data - 服务器响应数据
     * @returns {Object} 处理后的结果
     */
    handleLoginResponse(data) {
        switch (data.result) {
            case 0:
                // 登录成功
                return {
                    success: true,
                    message: '登录成功',
                    redirectUrl: data.msg,
                    data: data
                };
            case 2:
                // 需要完善企业信息
                return {
                    success: false,
                    needCompleteInfo: true,
                    message: '企业需完善企业通用基础信息表',
                    completeInfoUrl: 'https://ythzxfw.miit.gov.cn/userCenter',
                    redirectUrl: data.msg,
                    data: data
                };
            case 3:
                // 需要同意协议
                return {
                    success: false,
                    needAgreeProtocol: true,
                    message: '需要同意用户协议',
                    userId: data.msg,
                    data: data
                };
            case 4:
                // 需要完成注册
                return {
                    success: false,
                    needFinishRegister: true,
                    message: '需要完成注册流程',
                    userId: data.msg,
                    data: data
                };
            case 10:
                // 需要重置密码
                return {
                    success: false,
                    needResetPassword: true,
                    message: data.msg,
                    resetPasswordUrl: '/retrieve_password.jsp?toUrl=http%3A%2F%2Ftxjs.miit.gov.cn',
                    data: data
                };
            default:
                // 其他错误
                return {
                    success: false,
                    message: data.msg || '登录失败',
                    data: data
                };
        }
    }

    /**
     * 完整的登录流程封装
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.username - 用户名
     * @param {string} credentials.password - 密码
     * @param {string} [credentials.toUrl] - 目标URL
     * @returns {Promise<Object>} 登录流程结果
     */
    async loginFlow({ username, password, toUrl }) {
        try {
            console.log('🔄 开始登录流程...');
            
            // 1. 获取短信验证码
            console.log('步骤1: 获取短信验证码');
            const smsResult = await this.getSmsCode({ username, password });
            
            if (!smsResult.success) {
                return {
                    success: false,
                    step: 'getSmsCode',
                    message: smsResult.message,
                    error: smsResult.error
                };
            }

            return {
                success: true,
                step: 'getSmsCode',
                message: smsResult.message,
                nextStep: 'waitForSmsCode',
                loginFunction: (smsCode) => this.login({ username, password, smsCode, toUrl })
            };
            
        } catch (error) {
            console.error('登录流程失败:', error);
            return {
                success: false,
                step: 'loginFlow',
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 测试连接
     * @returns {Promise<Object>} 测试结果
     */
    async testConnection() {
        try {
            console.log('🧪 测试连接到工信部服务器...');
            
            const response = await fetch(`${this.baseUrl}/login.jsp`, {
                method: 'HEAD',
                mode: 'cors',
                credentials: 'include'
            });

            const result = {
                success: response.ok,
                status: response.status,
                statusText: response.statusText,
                message: response.ok ? '连接正常' : `连接失败: ${response.status} ${response.statusText}`
            };

            console.log('🧪 连接测试结果:', result);
            return result;

        } catch (error) {
            console.error('❌ 连接测试失败:', error);
            return {
                success: false,
                message: `连接失败: ${error.message}`,
                error: error
            };
        }
    }
}

export default MiitLoginApiExtension;
