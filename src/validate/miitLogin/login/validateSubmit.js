import { runValidationChain } from '@/util/validationChain'
import handleLogin from '@/validate/miitLogin/business/handleLogin'
import handleData from '@/validate/miitLogin/business/handleData'

export default async function validateSubmit(context) {
    context.shouldSkip = false;
    // 可通过接口或配置动态获取
    const configFromApi = {
        handleLoginEnable: true,
        handleDataEnable: true,
    };
    const chain = [
        {
            name: '校验登录会话是否有效',
            enable: configFromApi.handleLoginEnable,
            skipOnFail: false,
            validate: handleLogin
        },
        {
            name: '校验数据是否导入成功',
            enable: configFromApi.handleDataEnable,
            skipOnFail: false,
            validate: handleData
        }
    ];
    return await runValidationChain(chain, context);
}