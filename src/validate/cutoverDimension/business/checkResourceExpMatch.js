import {getCompanyList, getOrderTaskList} from "@/validate/cutoverDimension/utils/pageElementUtils";
import {getResourceByValiResult} from "@/api/cutoverDimensionApp";

export default async function checkResourceExpMatch(context) {
    const taskList = getOrderTaskList();
    const taskCodeArr = taskList.map(item => item.taskCode);
    const orgIdList = await getCompanyList();
    const orgIdStr = orgIdList.join(",");
    let params = {
        orgId : orgIdStr,
        isMatched: 1,
        projectCode: this.baseOrderInfo.projectCode,
        taskNumberList: taskCodeArr,
        startRow: 1,
        pageSize: 10
    }
    const response = await getResourceByValiResult(params);
    if (response.code === '10000') {
        const failedItems = response.data && response.data.failedItems ? response.data.failedItems : [];
        context.showDialog('cutoverDimensionDialog', {
            resourceExpMatchFailedList: failedItems,
            resourceExpMatchDialogVisible: true
        });
    }
    return {
        isValid: true,
        failedItems: [],
        validateMessage: ''
    };
}