import {
    getAcceptanceDate,
    getBaseOrderInfo,
    getCompanyList,
    getOrderTaskList
} from "@/validate/cutoverDimension/utils/pageElementUtils";
import {getResourceByValiResult} from "@/api/cutoverDimensionApp";
import {EamLoading} from "@/util/EamElementExt";
import {logValidation} from "@/api/commonApi";
import { Loading, Message } from 'element-ui';
// 常量定义
const VALIDATION_RESULT = {
    PASS: true,
    FAIL: false
};

const SKIP_REASONS = {
    NO_PROJECT_CODE: '未获取到项目编码，跳过校验',
    NO_ACCEPTANCE_DATE: '未获取到交维日期，跳过校验',
    NO_TASKS: '任务列表为空，跳过校验',
    EMPTY_EVALUATION: '隐患清单评估结论为空，跳过校验'
};

/**
 * 校验资源维护数量与支出数量是否匹配
 * @param {Object} context - 上下文对象，用于传递数据给调用方
 * @returns {Promise<boolean>} 校验结果 true-通过，false-不通过
 */
export default async function checkResourceExpMatch(context) {
    let errorMessage;
    let isSuccessful = true;
    let valiResult = true;
    // 获取工单基础信息
    const baseOrderInfo = getBaseOrderInfo();
    try {
        Loading.service({
            lock: true,
            fullscreen: true,
            text: '校验中，请勿关闭界面',
            customClass: 'eams-chrome-ext-loading',
            background: 'rgba(89,89,89,0.8)',
            zIndex: 9999999999
        });
        // 五秒后执行某些操作
        setTimeout(function() {
            // 这里写五秒后要执行的代码
            plugin.log("五秒已经过去了");
        }, 5000); // 5000 表示毫秒，即 5 秒
        // 前置条件检查：项目编码
        if (!baseOrderInfo.projectCode) {
            plugin.log(SKIP_REASONS);
            return VALIDATION_RESULT.PASS;
        }
        // 前置条件检查：验收日期
        if (!getAcceptanceDate()) {
            plugin.log(SKIP_REASONS.NO_ACCEPTANCE_DATE);
            return VALIDATION_RESULT.PASS;
        }
        const orderTaskList = getOrderTaskList();
        // 前置条件检查：任务列表
        if (orderTaskList === null || orderTaskList.length === 0) {
            plugin.log(SKIP_REASONS.NO_TASKS);
            return VALIDATION_RESULT.PASS;
        }

        // 前置条件检查：隐患清单评估结论校验
        if (baseOrderInfo.showHiddenDangerList) {
            const emptyEvaluationItems = baseOrderInfo.hiddenDangerList.filter(item =>
                item.evaluationConclusion === null || item.evaluationConclusion === undefined
            );
            if (emptyEvaluationItems.length > 0) {
                plugin.log(SKIP_REASONS.EMPTY_EVALUATION);
                return VALIDATION_RESULT.PASS;
            }
        }

        // 获取任务列表和公司列表
        const taskList = getOrderTaskList();
        const taskCodeArr = taskList.map(item => item.taskCode);

        // 获取公司ID列表
        const orgIdList = await getCompanyList();
        const orgIdStr = orgIdList.join(",");

        // 构建请求参数
        const requestParams = buildRequestParams(baseOrderInfo.projectCode, orgIdStr, taskCodeArr);

        // 调用资源校验接口
        const response = await getResourceByValiResult(requestParams);

        // 处理校验结果
        return handleValidationResponse(response, context);

    } catch (error) {
        plugin.error('校验资源维护数量与支出数量匹配时发生错误:', error);
        isSuccessful = false;
        errorMessage = error.message;
        // 发生错误时返回通过，避免阻塞流程
        return VALIDATION_RESULT.PASS;
    }  finally {
        EamLoading.close();
        const taskList = getOrderTaskList();
        const taskCodeArr = taskList.map(item => item.taskCode);
        await logValidation({
            businessType: 'CHECK_CUTOVER_SUBMIT',
            projectCode: baseOrderInfo.projectCode,
            taskCodes: taskCodeArr.join(","),
            validationResults: valiResult,
            requestParams: null,
            responseData: null,
            isSuccessful: isSuccessful,
            errorMessages: errorMessage,
            pageUrl: window.location.href
        })
    }
}

/**
 * 构建请求参数
 * @param {string} projectCode - 项目编码
 * @param {string} orgIdStr - 组织ID字符串
 * @param {Array} taskCodeArr - 任务编码数组
 * @returns {Object} 请求参数对象
 */
function buildRequestParams(projectCode, orgIdStr, taskCodeArr) {
    return {
        orgId: orgIdStr,
        isMatched: 1,
        projectCode: projectCode,
        taskNumberList: taskCodeArr,
        startRow: 1,
        pageSize: 10
    };
}

/**
 * 处理校验响应结果
 * @param {Object} response - API响应结果
 * @param {Object} context - 上下文对象
 * @returns {boolean} 校验结果
 */
function handleValidationResponse(response, context) {
    // 检查响应状态码
    if (response.code === '10000') {
        const failedItems = response.data?.failedItems || [];

        // 如果有失败项，显示对话框并返回校验失败
        if (failedItems.length > 0) {
            const formattedFailedItems = formatFailedItems(failedItems);

            context.showDialog('cutoverDimensionDialog', {
                resourceExpMatchFailedList: formattedFailedItems,
                resourceExpMatchDialogVisible: true
            });

            plugin.warn(`发现 ${formattedFailedItems.length} 条资源维护数量与支出数量不匹配的记录`);
            return VALIDATION_RESULT.FAIL;
        }
    }

    // 默认返回通过
    return VALIDATION_RESULT.PASS;
}

/**
 * 格式化失败项数据，添加序号等信息
 * @param {Array} failedItems - 失败项数组
 * @returns {Array} 格式化后的失败项数组
 */
function formatFailedItems(failedItems) {
    return failedItems.map((item, index) => ({
        ...item,
        index: index + 1
    }));
}