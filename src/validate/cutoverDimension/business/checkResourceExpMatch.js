import {
    getBaseOrderInfo,
    getCompanyList,
    getOrderTaskList
} from "@/validate/cutoverDimension/utils/pageElementUtils";
import {getResourceByValiResult} from "@/api/cutoverDimensionApp";
import {logValidation} from "@/api/commonApi";
// 常量定义
const VALIDATION_RESULT = {
    PASS: true,
    FAIL: false
};
/**
 * 校验资源维护数量与支出数量是否匹配
 * @param {Object} context - 上下文对象，用于传递数据给调用方
 * @returns {Promise<boolean>} 校验结果 true-通过，false-不通过
 */
export default async function checkResourceExpMatch(context) {
    let errorMessage;
    let isSuccessful = true;
    let valiResult = true;
    if(context.shouldSkip) {
        plugin.log('基础校验未通过，直接放行');
        return context.shouldSkip
    }
    try {
        // 获取基础订单信息
        const baseOrderInfo = getBaseOrderInfo();

        // 获取任务列表和公司列表
        const taskList = getOrderTaskList();
        const taskCodeArr = taskList.map(item => item.taskCode);

        // 获取公司ID列表
        const orgIdList = await getCompanyList();
        const orgIdStr = orgIdList.join(",");

        // 构建请求参数
        const requestParams = buildRequestParams(baseOrderInfo.projectCode, orgIdStr, taskCodeArr);

        // 调用资源校验接口
        const response = await getResourceByValiResult(requestParams);

        // 处理校验结果
        const result = handleValidationResponse(response, context);
        valiResult = result;
        return result;

    } catch (error) {
        plugin.error('校验资源维护数量与支出数量匹配时发生错误:', error);
        isSuccessful = false;
        errorMessage = error.message;
        // 发生错误时返回通过，避免阻塞流程
        return VALIDATION_RESULT.PASS;
    } finally {
        // 记录校验日志
        try {
            const baseOrderInfo = getBaseOrderInfo();
            const taskList = getOrderTaskList();
            const taskCodeArr = taskList.map(item => item.taskCode);

            await logValidation({
                businessType: 'CHECK_CUTOVER_SUBMIT',
                projectCode: baseOrderInfo.projectCode,
                taskCodes: taskCodeArr.join(","),
                validationResults: valiResult,
                requestParams: null,
                responseData: null,
                isSuccessful: isSuccessful,
                errorMessages: errorMessage,
                pageUrl: window.location.href
            });
        } catch (logError) {
            plugin.error('记录校验日志失败:', logError);
        }
    }
}

/**
 * 构建请求参数
 * @param {string} projectCode - 项目编码
 * @param {string} orgIdStr - 组织ID字符串
 * @param {Array} taskCodeArr - 任务编码数组
 * @returns {Object} 请求参数对象
 */
function buildRequestParams(projectCode, orgIdStr, taskCodeArr) {
    return {
        orgId: orgIdStr,
        isMatched: 1,
        projectCode: projectCode,
        taskNumberList: taskCodeArr,
        startRow: 1,
        pageSize: 10
    };
}

/**
 * 处理校验响应结果
 * @param {Object} response - API响应结果
 * @param {Object} context - 上下文对象
 * @returns {boolean} 校验结果
 */
function handleValidationResponse(response, context) {
    // 检查响应状态码
    if (response.code === '10000') {
        const failedItems = response.data?.failedItems || [];

        // 如果有失败项，显示对话框并返回校验失败
        if (failedItems.length > 0) {
            const formattedFailedItems = formatFailedItems(failedItems);

            context.showDialog('cutoverDimensionDialog', {
                resourceExpMatchFailedList: formattedFailedItems,
                resourceExpMatchDialogVisible: true
            });

            plugin.warn(`发现 ${formattedFailedItems.length} 条资源维护数量与支出数量不匹配的记录`);
            return VALIDATION_RESULT.FAIL;
        }
    }

    // 默认返回通过
    return VALIDATION_RESULT.PASS;
}

/**
 * 格式化失败项数据，添加序号等信息
 * @param {Array} failedItems - 失败项数组
 * @returns {Array} 格式化后的失败项数组
 */
function formatFailedItems(failedItems) {
    return failedItems.map((item, index) => ({
        ...item,
        index: index + 1
    }));
}