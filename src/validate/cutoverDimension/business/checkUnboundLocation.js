import {
    getAcceptanceDate,
    getBaseOrderInfo,
    getOrderTaskList
} from "@/validate/cutoverDimension/utils/pageElementUtils";
import {getTaskInfoById} from "@/api/cutoverDimensionApp";
import {EamLoading} from "@/util/EamElementExt";
import {logValidation} from "@/api/commonApi";

// 常量定义
const VALIDATION_RESULT = {
    PASS: true,
    FAIL: false
};

const SKIP_REASONS = {
    NO_PROJECT_CODE: '未获取到项目编码，跳过校验',
    NO_ACCEPTANCE_DATE: '未获取到交维日期，跳过校验',
    NO_TASKS: '任务列表为空，跳过校验',
    EMPTY_EVALUATION: '隐患清单评估结论为空，跳过校验'
};

/**
 * 校验任务是否绑定资产地点编码
 * @param {Object} context - 上下文对象，用于传递数据给调用方
 * @returns {Promise<boolean>} 校验结果 true-通过，false-不通过
 */
export default async function checkUnboundLocation(context) {
    let errorMessage;
    let isSuccessful = true;
    let valiResult = true;
    // 获取工单基础信息
    const baseOrderInfo = getBaseOrderInfo();
    try {
        EamLoading.service();

        // 前置条件检查：项目编码
        if (!baseOrderInfo.projectCode) {
            plugin.log(SKIP_REASONS);
            return VALIDATION_RESULT.PASS;
        }
        // 前置条件检查：验收日期
        if (!getAcceptanceDate()) {
            plugin.log(SKIP_REASONS.NO_ACCEPTANCE_DATE);
            return VALIDATION_RESULT.PASS;
        }
        const orderTaskList = getOrderTaskList();
        // 前置条件检查：任务列表
        if (orderTaskList || orderTaskList.length === 0) {
            plugin.log(SKIP_REASONS.NO_TASKS);
            return VALIDATION_RESULT.PASS;
        }

        // 前置条件检查：隐患清单评估结论校验
        if (baseOrderInfo.showHiddenDangerList) {
            const emptyEvaluationItems = baseOrderInfo.hiddenDangerList.filter(item =>
                item.evaluationConclusion === null || item.evaluationConclusion === undefined
            );
            if (emptyEvaluationItems.length > 0) {
                plugin.log(SKIP_REASONS.EMPTY_EVALUATION);
                return VALIDATION_RESULT.PASS;
            }
        }

        const taskDetailPromises = orderTaskList.map(item =>
            getTaskInfoById({ id: item.taskId })
                .then(res => res.data.body)
                .catch(error => {
                    plugin.error(`获取任务详情失败，任务ID: ${item.taskId}`, error);
                    return null;
                })
        );

        const taskDetails = await Promise.all(taskDetailPromises);

        // 过滤掉获取失败的任务，并找出未绑定资产地点编码的任务
        const validTaskDetails = taskDetails.filter(task => task !== null);
        const unboundLocationTasks = validTaskDetails.filter(task => !task.assetRegionCode);

        // 如果存在未绑定的任务，显示对话框并返回校验失败
        if (unboundLocationTasks.length > 0) {
            const unbindTasksInfo = buildUnbindTasksInfo(unboundLocationTasks, taskList);

            context.showDialog('cutoverDimensionDialog', {
                unboundLocationTaskList: unbindTasksInfo,
                unboundLocationDialogVisible: true
            });
            errorMessage = '割接上线交维站点未关联资产地点';
            valiResult = false;
            plugin.warn(`发现 ${unbindTasksInfo.length} 个未绑定资产地点编码的任务`);
            return VALIDATION_RESULT.FAIL;
        }

        return VALIDATION_RESULT.PASS;

    } catch (error) {
        plugin.error('校验任务资产地点编码绑定时发生错误:', error);
        isSuccessful = false;
        errorMessage = error.message;
        // 发生错误时返回通过，避免阻塞流程
        return VALIDATION_RESULT.PASS;
    }  finally {
        EamLoading.close();
        const taskList = getOrderTaskList();
        const taskCodeArr = taskList.map(item => item.taskCode);
        await logValidation({
            businessType: 'CHECK_CUTOVER_SUBMIT',
            projectCode: baseOrderInfo.projectCode,
            taskCodes: taskCodeArr.join(","),
            validationResults: valiResult,
            requestParams: null,
            responseData: null,
            isSuccessful: isSuccessful,
            errorMessages: errorMessage,
            pageUrl: window.location.href
        })
    }
}

/**
 * 构建未绑定任务的详细信息
 * @param {Array} unboundTasks - 未绑定资产地点编码的任务列表
 * @param {Array} originalTaskList - 原始任务列表
 * @returns {Array} 格式化后的未绑定任务信息
 */
function buildUnbindTasksInfo(unboundTasks, originalTaskList) {
    return unboundTasks.map((task, index) => {
        const originalTask = originalTaskList.find(t => t.taskId === task.id);
        return {
            index: index + 1,
            taskId: task.id,
            taskCode: task.taskCode || '',
            taskName: task.taskLongName || '',
            taskType: task.taskTypeName || '',
            status: originalTask?.status || '未知',
            assetRegionCode: task.assetRegionCode || null
        };
    });
}
