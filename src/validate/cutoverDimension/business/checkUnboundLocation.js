import {
    getAcceptanceDate,
    getBaseOrderInfo,
    getOrderTaskList
} from "@/validate/cutoverDimension/utils/pageElementUtils";
import {getTaskInfoById} from "@/api/cutoverDimensionApp";

export default async function checkUnboundLocation(context) {
    const baseOrderInfo = getBaseOrderInfo();
    if(!baseOrderInfo.projectCode || ! getAcceptanceDate() || getOrderTaskList().length === 0) {
        return;
    }
    if(baseOrderInfo.showHiddenDangerList) {
        // 校验隐患清单中的评估结论不能为空(null)，必须是0或1
        const emptyEvaluationItems = this.baseOrderInfo.hiddenDangerList.filter(item =>
            item.evaluationConclusion === null || item.evaluationConclusion === undefined
        );
        if (emptyEvaluationItems.length > 0) {
            return;
        }
    }

    const taskList = getOrderTaskList();
    const tasks = taskList.map(item =>
        getTaskInfoById({ id: item.taskId }).then(res => res.data.body)
    );
    const results = await Promise.all(tasks);
    const emptyLocTaskList = results.filter(item => !item.assetRegionCode);

    // 构建未绑定任务的详细信息
    const unbindTasks = emptyLocTaskList.map((task, index) => {
        const originalTask = taskList.find(t => t.taskId === task.id);
        return {
            index: index + 1,
            taskId: task.id,
            taskCode: task.taskCode || '',
            taskName: task.taskLongName || '',
            taskType: task.taskTypeName || '',
            status: originalTask?.status || '未知',
            assetRegionCode: task.assetRegionCode || null
        };
    });

    return {
        isValid: emptyLocTaskList.length === 0,
        unbindTasks: unbindTasks
    };
    return isValid;
}