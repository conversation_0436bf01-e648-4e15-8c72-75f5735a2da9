import checkUnboundLocation from '../business/checkUnboundLocation'
import { runValidationChain } from '@/util/validationChain'

export default async function validateSubmit(context) {
  // 可通过接口或配置动态获取
  const configFromApi = {
    unboundLocationEnable: true,
    resourceValidationEnable: true,
  };
  const chain = [
    {
      name: '任务未关联资产地点编码校验',
      enable: configFromApi.unboundLocationEnable,
      skipOnFail: false,
      validate: checkUnboundLocation
    },
    {
      name: '资源维护数量与支出数量不匹配校验',
      enable: configFromApi.resourceValidationEnable,
      skipOnFail: false,
      validate: checkUnboundLocation
    }
  ];
  return await runValidation<PERSON>hain(chain, context);
} 