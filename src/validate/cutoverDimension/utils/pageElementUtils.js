import {getCurrentCompanyList} from "@/api/commonApi";

export function getBaseOrderInfo() {
  return document.querySelector(".main-container").__vue__.$vnode.parent.componentInstance.$data;
}
export function getOrderTaskList() {
  return getBaseOrderInfo().taskListData;
}

export function getAcceptanceDate() {
  return document.querySelector(".main-container .order_form_content .el-date-editor--date .el-input__inner").value;
}

export async function getCompanyList() {
  const companyList = await getCurrentCompanyList().then(res => res.data.body.list);
  return companyList.map(item => item.orgId);
}