import {getCurrentCompanyList} from "@/api/commonApi";

/**
 * 获取项目编码
 * @returns {string|null}
 */
export function getProjectCode() {
  const projectCodeDivDom = document.querySelectorAll(".el-form-item__content")[1];
  if (projectCodeDivDom) {
    const projectCode = projectCodeDivDom.querySelector("input").value;
    return projectCode ? projectCode : null;
  }
  return null;
}

/**
 * 获取组织ID
 * @returns {Promise<*|null>}
 */
export async function getOrgId() {
  const companyName = document.querySelector("#companyName input").value;
  if (companyName) {
    const companyList = await getCurrentCompanyList().then(res => res.data.body.list);
    const companyItem = companyList.find(item => item.companyName === companyName);
    return companyItem ? companyItem.orgId : null;
  }
  return null;
}

/**
 * 获取全量推送的选择框的值
 * @returns {*}
 */
export function getFullPushChecked() {
  return document.querySelector('#pane-addAssets .table-top .el-checkbox__original').checked;
}

/**
 * 获取勾选的资产
 * @returns {*}
 */
export function getCheckAssets() {
  return document.querySelector("#pane-addAssets .el-table").__vue__.selection;
}