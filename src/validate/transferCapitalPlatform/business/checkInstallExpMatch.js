import {logValidation} from "@/api/commonApi";
import {checkInstallAssetQuantity} from "@/api/transferCapitalPlatformApp";
import {EamLoading} from "@/util/EamElementExt"
import {getCheckAssets, getFullPushChecked, getOrgId, getProjectCode} from '../utils/pageElementUtils'

// 常量定义
const EXCLUDED_ASSET_STATUS = ['已签收', '签收中'];
const FAIL_CODE = "10000";
const BUSINESS_TYPE = 'CHECK_TRANSFER_SUBMIT';

/**
 * 校验安装单支出数量与资产数量是否匹配
 * @param {Object} context - 上下文对象，用于传递数据给调用方
 * @returns {Promise<boolean>} 校验结果
 */
export default async function checkInstallExpMatch(context) {
    // 1. 校验前置条件
    const projectCode = getProjectCode();
    if (!projectCode) {
        plugin.warn('未获取到项目编码，跳过校验');
        return true;
    }

    const checked = getFullPushChecked();
    const assets = getCheckAssets();

    // 如果既没有全量推送也没有选中资产，跳过校验
    if (!checked && (!assets || assets.length === 0)) {
        plugin.log('未选择全量推送且无选中资产，跳过校验');
        return true;
    }

    // 2. 处理资产数据
    let assetIds = null;
    if (assets && assets.length > 0) {
        // 筛选符合条件的资产（排除已签收和签收中的资产）
        const filteredAssets = assets.filter(asset =>
            !EXCLUDED_ASSET_STATUS.includes(asset.assetStatusCode)
        );

        if (filteredAssets.length === 0) {
            plugin.log('所有选中资产均为已签收或签收中状态，跳过校验');
            return true;
        }

        assetIds = filteredAssets.map(asset => asset.id);
    }

    const orgId = await getOrgId();
    if (!orgId) {
        plugin.warn('未获取到组织ID，跳过校验');
        return true;
    }

    // 3. 执行校验
    EamLoading.service();
    let isValid = true;
    let errorMessage = null;
    let isSuccessful = true;
    let responseData = null;

    const params = {
        orgId,
        assetIdList: assetIds,
        projectCode,
        fullPushChecked: checked
    };

    try {
        const res = await checkInstallAssetQuantity(params);
        responseData = res;
        if (res.code === FAIL_CODE) {
            isValid = false;
            // 为表格数据添加序号
            const unmatchedAssetList = res.data.map((item, index) => ({
                ...item,
                index: index + 1
            }));
            context.showDialog('transferCapitalPlatformDialog', {
                unmatchedAssetList
            });
            plugin.warn(`发现 ${unmatchedAssetList.length} 条不匹配的资产记录`);
        } else {
            plugin.log('安装单数量与支出数量校验通过');
        }
    } catch (error) {
        plugin.error('校验安装单数量与支出数量是否匹配校验时发生错误:', error);
        errorMessage = error.message || '校验过程中发生未知错误';
        isSuccessful = false;
    } finally {
        EamLoading.close();
        // 记录校验日志
        try {
            await logValidation({
                businessType: BUSINESS_TYPE,
                projectCode,
                taskCodes: null,
                validationResults: isValid,
                requestParams: JSON.stringify(params),
                responseData: JSON.stringify(responseData),
                isSuccessful,
                errorMessages: errorMessage,
                pageUrl: window.location.href
            });
        } catch (logError) {
            plugin.error('记录校验日志失败:', logError);
        }
    }
    return isValid;
}