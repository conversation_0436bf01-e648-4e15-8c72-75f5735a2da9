import {logValidation} from "@/api/commonApi";
import {checkInstallAssetQuantity} from "@/api/transferCapitalPlatformApp";
import {EamLoading} from "@/util/EamElementExt"
import {getCheckAssets, getFullPushChecked, getOrgId, getProjectCode} from '../utils/pageElementUtils'

export default async function checkInstallExpMatch(context) {
    /**
     * 1. 校验前置条件开始
     */
    const projectCode = getProjectCode();
    if (!projectCode) {
        return true;
    }
    const checked = getFullPushChecked();
    const assets = getCheckAssets();
    if((!checked && assets.length === 0)){
        return;
    }
    let assetIds;
    if(assets && assets.length > 0) {
        assetIds = assets.map(asset => asset.id);
        // 筛选符合条件的资产
        const filteredAssets = assets.filter(asset =>
            !['已签收', '签收中'].includes(asset.assetStatusCode)
        );
        if (filteredAssets.length === 0) {
            return;
        }
        assetIds = filteredAssets.map(asset => asset.id);
    }

    const orgId = await getOrgId();
    if(!orgId){
        return;
    }
    /**
     * 1. 校验前置条件结束
     */
    EamLoading.service();
    let isValid = true;
    let errorMessage;
    let isSuccessful = true;
    const params = {
        orgId: orgId,
        assetIdList: assetIds,
        projectCode: projectCode,
        fullPushChecked: checked
    }
    try {
        const res = await checkInstallAssetQuantity(params)
        if (res.code === "10000") {
            isValid = false
            // 为表格数据添加序号
            this.unmatchedAssetList = res.data.map((item, index) => ({
                ...item,
                index: index + 1
            }));
            this.dialogVisible = true;
        } else {
        }
    } catch (error) {
        plugin.error(error);
        errorMessage = error.message;
        isSuccessful = false;
    } finally {
        EamLoading.close();
        await logValidation({
            businessType: 'CHECK_TRANSFER_SUBMIT',
            projectCode: projectCode,
            taskCodes: null,
            validationResults: isValid,
            requestParams: JSON.stringify(params),
            responseData: null,
            isSuccessful: isSuccessful,
            errorMessages: errorMessage,
            pageUrl: window.location.href
        })
    }
}