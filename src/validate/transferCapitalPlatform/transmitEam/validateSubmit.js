import checkInstallExpMatch from '../business/checkInstallExpMatch'
import { runValidationChain } from '@/util/validationChain'

export default async function validateSubmit(context) {
  // 可通过接口或配置动态获取
  const configFromApi = {
    installExpMatchEnable: true
  };
  const chain = [
    {
      name: '支出数量不能大于物资安装量校验',
      enable: configFromApi.installExpMatchEnable,
      skipOnFail: false,
      validate: checkInstallExpMatch
    }
  ];
  return await runValidation<PERSON>hain(chain, context);
} 