import {getColumnIndex, getTableRows, getTaskCodeFromRow} from '../utils/pageElementUtils'
import { checkPendingWorkOrderExists, queryReport } from '@/api/completionReportApp'
import {getCurrentCompanyList, logValidation} from '@/api/commonApi'
import { Message } from 'element-ui'
import {getSpanValue} from "@/util/selector";
import {EamLoading} from "@/util/EamElementExt";

export default async function materialWarningApprove(context) {
  console.log(context)
  // 1. 校验前置条件
  const projectCode = getSpanValue('projectCode');
  const projectNumber = getSpanValue('completionReportNo')

  if (!projectCode || !projectNumber || context.showTip === false || context.shouldSkip) {
    return true;
  }

  try {
    EamLoading.service();
    // 3. 检查待办
    let taskId = '';
    const workOrderExists = await checkPendingWorkOrderExists({ projectNumber });
    if (workOrderExists.code === '0000' && Array.isArray(workOrderExists.data)) {
      for (const item of workOrderExists.data) {
        // 1 、是否存在低于设计量的流程待办，存在则直接阻断
        if (item.status === 'in_process' && item.warnType === '1'){
          Message.warning('当前存在预警支出数量确定单审批，请等待确定后进行操作');
          return false;
        }
        if (item.status === 'completed' && item.warnType === '1'){
          taskId = item.id;
        }
      }
    }
    // 4. 获取表格任务编码
    const columnIndex = getColumnIndex('任务编码');
    const rows = getTableRows();
    console.log(rows);
    const requestParamList = rows.map(row => ({
      projectCode,
      projectNumber,
      taskCode: getTaskCodeFromRow(row, columnIndex)
    }));
    plugin.log("校验的任务编号：", requestParamList);

    const companyList = await getCurrentCompanyList().then(res => res.data.body.list);
    const orgIds = companyList.map(item => item.orgId);
    plugin.log("组织ID:", orgIds);

    // 5. 查询接口，获取预警数据
    let belowDesignData = [];
    let aboveDesignData = [];
    let allData = [];
    let validationResult = false;
    let showDialog = false;
    let errorMessages = `存在物资支出设计数据差异数据：完工报告单号${projectNumber};项目编号${projectCode} `;
    const response = await queryReport(requestParamList, taskId, orgIds.join(","));
    console.log(response);
    if (response.code === '0000' && response.data.length > 0) {
      belowDesignData = response.data.filter(item => item.differenceType === '1');
      aboveDesignData = response.data.filter(item => item.differenceType === '2');
      allData = [...belowDesignData, ...aboveDesignData];
      if (belowDesignData.length === 0 && aboveDesignData.length === 0) {
        validationResult = true;
      }
      showDialog = true;
    } else {
      errorMessages = response.msg + `：完工报告单号${projectNumber};项目编号${projectCode} `;
    }
    // 6. 日志
    await logValidation({
      businessType: 'COMPLETION_REPORT',
      projectCode,
      validationResults: validationResult,
      requestParams: JSON.stringify(requestParamList),
      responseData: JSON.stringify(allData),
      isSuccessful: validationResult,
      errorMessages: validationResult ? '' : errorMessages,
      pageUrl: window.location.href
    });
    // 7. 弹窗
    if (showDialog) {
      context.showDialog &&
        context.showDialog('MaterialWarningDialog', {
          belowDesignData,
          aboveDesignData
        });
      return false;
    }
    if (!validationResult) {
      Message.error(response.msg);
      return false;
    }
    return true;
  } catch (error) {
    Message.error('物资支出预警校验失败，请稍后重试');
    return false;
  } finally {
    EamLoading.close();
  }
} 