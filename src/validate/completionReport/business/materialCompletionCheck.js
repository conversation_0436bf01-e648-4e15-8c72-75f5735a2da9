import {getColumnIndex, getTableRows, getTaskCodeFromRow} from '../utils/pageElementUtils'
import { logValidation } from '@/api/commonApi'
import { materialRequiredCheck} from '@/api/completionReportApp'
import { Message } from 'element-ui'
import {EamLoading} from "@/util/EamElementExt";
import {getInputValue, getSpanValue} from "@/util/selector";

export default async function materialCompletionCheck(context) {
  console.log(context)
  // 1. 校验前置条件
  const projectCode = getInputValue('projectCode') || getSpanValue('projectCode');
  const projectName = getSpanValue('projectName');
  const projectNumber = getSpanValue('completionReportNo')

  if (!projectCode || context.showTip === false) {
    return true;
  }

  try {
    EamLoading.service();
    // 2. 获取任务编码信息
    const columnIndex = getColumnIndex('任务编码');
    const taskNameIndex = getColumnIndex('任务名称');
    const rows = getTableRows();
    console.log(rows);
    const requestParamList = {
      projectCode,
      projectName,
      taskList: rows.map(row => ({
        taskCode: getTaskCodeFromRow(row, columnIndex),
        taskName: getTaskCodeFromRow(row, taskNameIndex)
      })),
      checkNodeType: 'material_completion'
    };
    plugin.log("资料必填性校验开始：的任务编号：", requestParamList);
    // 3.校验资料完整性
    let missFileData = [];
    let validationResult = false;
    let showDialog = false;
    let errorMessages = `资料缺失：完工报告单号${projectNumber};项目编号${projectCode} `;
    //调用校验方法
    const response = await materialRequiredCheck(requestParamList);
    console.log(response);
    if (response.code === '0000' && response.data.success) {
      //校验通过
      validationResult = true;
    } else {
      missFileData = response.data.missingMaterials;
      showDialog=true;
      errorMessages = response.data.validateMessage;
    }
    // 6. 日志
    await logValidation({
      businessType: 'MATERIAL_REQUIRED_CHECK',
      projectCode,
      validationResults: validationResult,
      requestParams: JSON.stringify(requestParamList),
      responseData: JSON.stringify(missFileData),
      isSuccessful: validationResult,
      errorMessages: validationResult ? '' : errorMessages,
      pageUrl: window.location.href
    });
    // 7. 弹窗
    if (showDialog) {
      context.showDialog &&
      context.showDialog('MaterialRequiredCheckDialog', {
        missFileData
      });
      return false;
    }
    if (!validationResult) {
      Message.error(response.msg);
      return false;
    }
    return true;
  } catch (error) {
    Message.error('实施/监理资料完整性校验失败，请稍后重试');
    return false;
  } finally {
    EamLoading.close();
  }
} 