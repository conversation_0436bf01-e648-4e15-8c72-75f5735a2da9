import { getPayVerification} from '@/api/completionReportApp'
import { logValidation } from '@/api/commonApi'

import {EamLoading} from "@/util/EamElementExt";
import {MessageBox} from "element-ui";


export default async function qxCheckEnable(context) {
    EamLoading.service();
    try {
        // 显示加载状态

        // 获取字段
        const rows = document.querySelectorAll('.main-warpper .el-table:first-child .el-table__body-wrapper .el-table__row');
        const areaIndex = getColumnIndexByChineseName('任务所属区域');
        const unitIndex = getColumnIndexByChineseName('施工单位');

        // 错误处理和容错
        if (areaIndex === -1 || unitIndex === -1) {
            console.error('未能找到指定的中文字段列');
            return false;
        }

        // 拼装状态
        const result = Array.from(rows).map(row => {
            const cells = row.querySelectorAll('td');
            const regionText = cells[areaIndex]?.textContent.trim() || '';
            const unitText = cells[unitIndex]?.textContent.trim() || '';

            // 查找匹配的城市代码
            const matchedCity = Object.keys(cityCodeMap).find(city => regionText.includes(city));
            return {
                areaIndex: matchedCity ? cityCodeMap[matchedCity] : regionText,
                unitIndex: unitText
            };
        }).filter(item => item.areaIndex || item.unitIndex);

        let validationResult = false;
        let responseData = null;

        let message=null;
        let isSuccessful=null;

        try {
            console.log('执行欠薪验证...')
            // 执行欠薪验证
         await getPayVerification(result, { withCredentials: true }).then(res => {
            var data = res.data;
             isSuccessful =data.isFlag
             message=data.message

         });
            EamLoading.close();


            if(!isSuccessful) {
                await MessageBox.alert(message, '提示', {
                    confirmButtonText: '确定',
                    type: 'warning'

                });
            }

            return isSuccessful;

        } catch (error) {
            validationResult = true;
            // 假设 plugin 已定义，若未定义需要处理
            plugin.error(`欠薪验证失败:`, error);
            return false;
        }finally {
            // 记录验证日志
            await logValidation({
                businessType: 'QX_CHECK_APP',
                taskCodes: null,
                projectCode: context.projectCode,
                validationResults: validationResult,
                requestParams: JSON.stringify(result),
                responseData: JSON.stringify(responseData),
                isSuccessful: validationResult,
                errorMessages: validationResult ? `欠薪校验失败` : '',
                pageUrl: window.location.href
            });
            EamLoading.close();
        }


    } catch (error) {
        console.error('验证过程中发生错误:', error);
        return false;
    } finally {
        // 关闭加载状态
        EamLoading.close();
    }
}

// 修复函数定义
// 修改函数以在任务明细表格内查找列索引
// 修改函数以在任务明细表格内查找列索引
function getColumnIndexByChineseName(chineseName) {
    // 参数校验
    if (typeof chineseName !== 'string') {
        console.warn('Invalid input: expected a string for chineseName');
        return -1;
    }

    // 只在第一个表格（任务明细表格）内查询表头元素，避免附件列表干扰
    const mainTable = document.querySelector('.main-warpper .el-table');
    if (!mainTable) {
        console.warn('未能找到任务明细主表格');
        return -1;
    }

    const headers = Array.from(mainTable.querySelectorAll('.el-table__header th'));
    return headers.findIndex(th => th.textContent.trim() === chineseName);
}
// 规范化 cityCodeMap 格式
const cityCodeMap = {
    '鹰潭市': '00370021000000000000',
    '新余市': '00370022000000000000',
    '九江市': '00370024000000000000',
    '上饶市': '00370025000000000000',
    '抚州市': '00370026000000000000',
    '南昌市': '00370088000000000000',
    '宜春市': '00370027000000000000',
    '吉安市': '00370028000000000000',
    '赣州市': '00370029000000000000',
    '景德镇市': '00370030000000000000',
    '萍乡市': '00370031000000000000',
};
