import {getColumnIndex, getTableRows, getTaskCodeFromRow} from '../utils/pageElementUtils'
import { checkPendingWorkOrderExists, queryReport } from '@/api/completionReportApp'
import {getCurrentCompanyList, logValidation} from '@/api/commonApi'
import { Message } from 'element-ui'
import {getInputValue, getSpanValue, getUserInfo} from "@/util/selector";
import {EamLoading} from "@/util/EamElementExt";

export default async function materialWarning(context) {
  // 1. 校验前置条件
  const projectCode = getInputValue('projectCode');
  const projectNumber = getSpanValue('completionReportNo')

  if (!projectCode || !projectNumber || context.showTip === false || context.shouldSkip) {
    return true;
  }

  let requestParamList = [];
  let allData = [];
  let validationResult = false;
  let errorMessages = '';

  try {
    // 2. 检查待办
    let taskId = '';
    const workOrderExists = await checkPendingWorkOrderExists({ projectNumber });
    if (workOrderExists.code === '0000' && Array.isArray(workOrderExists.data)) {
      for (const item of workOrderExists.data) {
        // 是否存在低于设计量的流程待办，存在则直接阻断
        if (item.status === 'in_process' && item.warnType === '1'){
          Message.warning('当前存在预警支出数量确定单审批，请等待确定后进行操作');
          return false;
        }
        if (item.status === 'completed' && item.warnType === '1'){
          taskId = item.id;
        }
      }
    }

    // 3. 获取表格任务编码
    const columnIndex = getColumnIndex('任务编码');
    const rows = getTableRows();
    plugin.log(rows);
    requestParamList = rows.map(row => ({
      projectCode,
      projectNumber,
      taskCode: getTaskCodeFromRow(row, columnIndex)
    }));
    plugin.log("校验的任务编号：", requestParamList);

    // 4. 获取公司列表
    const companyList = await getCurrentCompanyList().then(res => res.data.body.list);
    const orgIds = companyList.map(item => item.orgId);
    plugin.log("组织ID:", orgIds);
    // 5. 查询接口，获取预警数据
    const response = await queryReport(requestParamList, taskId, orgIds.join(","));
    plugin.log(response);

    let showDialog = false;
    let belowDesignData = [];
    let aboveDesignData = [];

    if (response.code === '0000' && response.data?.length > 0) {
      belowDesignData = response.data.filter(item => item.differenceType === '1');
      aboveDesignData = response.data.filter(item => item.differenceType === '2');
      allData = [...belowDesignData, ...aboveDesignData];

      validationResult = allData.length === 0;
      showDialog = true;

      if (!validationResult) {
        errorMessages = `存在物资支出设计数据差异数据：完工报告单号${projectNumber};项目编号${projectCode}`;
      }
    } else {
      validationResult = false;
      errorMessages = `${response.msg || '查询失败'}：完工报告单号${projectNumber};项目编号${projectCode}`;
    }

    // 6. 记录日志
    await logValidation({
      businessType: 'COMPLETION_REPORT',
      projectCode,
      validationResults: validationResult,
      requestParams: JSON.stringify(requestParamList),
      responseData: JSON.stringify(allData),
      isSuccessful: validationResult,
      errorMessages: validationResult ? '' : errorMessages,
      pageUrl: window.location.href
    });

    // 7. 处理结果
    if (showDialog && !validationResult) {
      context.showDialog?.('MaterialWarningDialog', {
        belowDesignData,
        aboveDesignData
      });
      return false;
    }

    if (!validationResult) {
      Message.error(response.msg || '物资支出预警校验失败');
      return false;
    }

    return true;
  } catch (error) {
    plugin.error('物资支出预警校验异常:', error);
    Message.error('物资支出预警校验失败，请稍后重试');

    // 记录异常日志
    try {
      await logValidation({
        businessType: 'COMPLETION_REPORT',
        projectCode,
        validationResults: false,
        requestParams: JSON.stringify(requestParamList),
        responseData: JSON.stringify({ error: error.message }),
        isSuccessful: false,
        errorMessages: `校验异常: ${error.message}`,
        pageUrl: window.location.href
      });
    } catch (logError) {
      plugin.error('记录异常日志失败:', logError);
    }

    return false;
  } finally {
    EamLoading.close();
  }
}