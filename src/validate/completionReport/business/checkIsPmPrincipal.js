import { getUserInfo, getSpanValue } from '@/util/selector'

export default async function checkIsPmPrincipal(context) {
  console.log(context);
  // 获取工程实施经理（主）
  const pmPrincipal = getSpanValue('pmPrincipal')
  // 获取当前用户
  const userInfo = getUserInfo();
  if (!userInfo || !(userInfo.userName === pmPrincipal || userInfo.userNameNew === pmPrincipal)) {
    plugin.warn('当前登录人非工程实施经理，跳过校验');
    context.shouldSkip = true;
    return false;
  }
  context.shouldSkip = false;
  return true;
} 