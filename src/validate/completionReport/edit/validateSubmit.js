import checkIsPmPrincipal from '../business/checkIsPmPrincipal'
import materialWarning from '../business/materialWarning'
import { runValidationChain } from '@/util/validationChain'
import qxCheckEnable from "@/validate/completionReport/business/qxCheckEnable";

export default async function validateSubmit(context) {
  // 可通过接口或配置动态获取
  const configFromApi = {
    pmPrincipalEnable: true,
    materialWarningEnable: true,
    qxCheckEnable:false
  };
  const chain = [
      {
          name: '欠薪校验',
          enable: configFromApi.qxCheckEnable,
          skipOnFail: false,
          validate: qxCheckEnable
      },
    {
      name: '工程实施经理校验',
      enable: configFromApi.pmPrincipalEnable,
      skipOnFail: true,
      validate: checkIsPmPrincipal
    },
    {
      name: '物资支出预警校验',
      enable: configFromApi.materialWarningEnable,
      skipOnFail: false,
      validate: materialWarning
    },

  ];
  return await runValidation<PERSON>hain(chain, context);
} 
