import checkIsPmPrincipal from '../business/checkIsPmPrincipal'
import materialWarning from '../business/materialWarning'
import { runValidationChain } from '@/util/validationChain'
import materialWarningApprove from "@/validate/completionReport/business/materialWarningApprove";
import materialCompletionCheck from '../business/materialCompletionCheck'

export default async function validateSubmit(context) {
  // 可通过接口或配置动态获取
  const configFromApi = {
    pmPrincipalEnable: true,
    materialWarningEnable: true,
    materialCompletionCheckEnable: false
  };
  const chain = [
    {
      name: '工程实施经理校验',
      enable: configFromApi.pmPrincipalEnable,
      skipOnFail: true,
      validate: checkIsPmPrincipal
    },
    {
      name: '物资支出预警校验',
      enable: configFromApi.materialWarningEnable,
      skipOnFail: false,
      validate: materialWarningApprove
    },
    {
      name: '实施/监理资料完整性校验',
      enable: configFromApi.materialCompletionCheckEnable,
      skipOnFail: false,
      validate: materialCompletionCheck
    }
  ];
  return await runValidationChain(chain, context);
} 