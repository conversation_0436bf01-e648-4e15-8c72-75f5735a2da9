// 获取表格所有行
export function getTableRows() {
  const rows = [];

  // 获取表格容器
  const table = document.querySelector('.main-warpper .el-table__body-wrapper');

  if (!table) {
    plugin.warn('未找到目标表格');
    return rows;
  }
  // 获取所有行
  const allRows = table.querySelectorAll('tr.el-table__row');

  allRows.forEach(row => {
    rows.push(row);
  });
  return rows;
}

// 获取某行的任务编码
export function getTaskCodeFromRow(row, columnIndex) {
  const cells = row.querySelectorAll('td')
  if (cells.length <= columnIndex) return ''
  const cell = cells[columnIndex]
  const tooltipElement = cell.querySelector('.el-tooltip')
  return tooltipElement ? tooltipElement.textContent.trim() : ''
}

// 获取调入任务编码table 所在列序号
export function getColumnIndex(title) {
  // 获取第一个表格的表头
  const firstTableHeader = document.querySelector('.main-warpper .el-table__header-wrapper .el-table__header');

  if (!firstTableHeader) {
    return -1;
  }

  const headerCells = firstTableHeader.querySelectorAll('.el-table__cell');
  // 遍历查找包含"任务编码"的单元格
  let targetIndex = -1;

  headerCells.forEach((cell, index) => {
    const cellText = cell.querySelector('.cell')?.textContent?.trim();
    if (cellText && cellText.includes(title)) {
      targetIndex = index;
    }
  });

  // 返回结果（注意：索引从0开始）
  plugin.log(title + '所在列的序号:', targetIndex);
  return targetIndex;
}