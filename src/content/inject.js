
function injectCode(src,url) {
    var temp = document.createElement('script');
    temp.setAttribute('type', 'text/javascript');
    // 获得的地址类似：chrome-extension://ihcokhadfjfchaeagdoclpnjdiokfakg/js/inject.js
    temp.src = src
    // temp.href = chrome.extension.getURL(jsPath);
    document.body.appendChild(temp);
    var div = document.createElement('div');
    div.id ='plugin-url'
    div.innerHTML =url
    div.style.display = 'none';
    document.body.appendChild(div);
}

injectCode(window.chrome.runtime.getURL('/js/content.js'),window.chrome.runtime.getURL(''));