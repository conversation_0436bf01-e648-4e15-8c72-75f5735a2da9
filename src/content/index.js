// // 通过Chrome插件的API加载字体文件

function injectCustomJs(jsPath)
{
    var temp = document.createElement('script');
    temp.setAttribute('type', 'text/javascript');
    temp.src =  process.env.VUE_APP_JS_HOST+jsPath;
    document.body.appendChild(temp);
}
function injectCustomCss(cssPath)
{
    let element_css = document.createElement('link');
    element_css.href =  process.env.VUE_APP_JS_HOST+cssPath;
    element_css.rel = "stylesheet"
    document.head.append(element_css);
}
function insertTools(){
    // injectCustomCss("css/view.css");
    injectCustomJs("/js/view.js?v=0.0.1&t=" + Date.now());
}

(function () {
    insertTools()
})()

