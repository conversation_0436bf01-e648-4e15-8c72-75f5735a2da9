/**
 * 工信部登录 - Chrome扩展内容脚本
 * 在工信部登录页面注入登录助手
 */

// 检查是否为工信部登录页面
function isMiitLoginPage() {
    return window.location.href.includes('ucenter.miit.gov.cn') && 
           window.location.pathname.includes('login.jsp');
}

// 等待页面加载完成
function waitForPageReady() {
    return new Promise((resolve) => {
        if (document.readyState === 'complete') {
            resolve();
        } else {
            window.addEventListener('load', resolve);
        }
    });
}

// 创建登录助手界面
function createLoginAssistant() {
    // 检查是否已存在
    if (document.getElementById('miit-login-assistant')) {
        return;
    }

    // 创建样式隔离的容器
    const assistant = createIsolatedAssistant();

    // 添加到页面
    document.body.appendChild(assistant);

    // 绑定事件
    bindAssistantEvents();
}

/**
 * 创建样式隔离的助手容器
 */
function createIsolatedAssistant() {
    const assistant = document.createElement('div');
    assistant.id = 'miit-login-assistant';

    // 设置容器的隔离样式 - 使用更高的z-index和样式重置
    assistant.style.cssText = `
        /* 基础定位 */
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        width: 320px !important;
        z-index: 2147483647 !important; /* 最高层级 */

        /* 样式重置 */
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        background: transparent !important;
        box-sizing: border-box !important;

        /* 字体重置 */
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
        color: #333 !important;

        /* 防止变形 */
        transform: none !important;
        filter: none !important;
        opacity: 1 !important;
        visibility: visible !important;

        /* 防止被原网页样式影响 */
        all: initial !important;
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        width: 320px !important;
        z-index: 2147483647 !important;
    `;

    assistant.innerHTML = `
        <div class="miit-assistant-main" style="
            /* 主容器样式 - 使用内联样式确保不被覆盖 */
            position: relative !important;
            width: 100% !important;
            background: #ffffff !important;
            border: 2px solid #409eff !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            color: #333333 !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
            /* 防止继承原网页样式 */
            all: initial !important;
            /* 重新设置必要样式 */
            display: block !important;
            position: relative !important;
            width: 100% !important;
            background: #ffffff !important;
            border: 2px solid #409eff !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        ">
            <div class="miit-assistant-header" style="
                /* 头部样式 */
                background: linear-gradient(135deg, #409eff, #67c23a) !important;
                color: #ffffff !important;
                padding: 12px 16px !important;
                border-radius: 6px 6px 0 0 !important;
                font-weight: bold !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                margin: 0 !important;
                border: none !important;
                box-sizing: border-box !important;
                font-family: inherit !important;
                font-size: 14px !important;
                line-height: 1.5 !important;
            ">
                <span>🔐 工信部登录助手</span>
                <button id="close-assistant">×</button>
            </div>
            <div class="miit-assistant-content" style="
                /* 内容区域样式 */
                padding: 16px !important;
                background: #ffffff !important;
                margin: 0 !important;
                border: none !important;
                box-sizing: border-box !important;
                font-family: inherit !important;
            ">
                <div class="miit-form-item">
                    <label>用户名:</label>
                    <input type="text" id="assist-username" placeholder="登录名/统一社会信用代码/身份证号">
                </div>
                <div class="miit-form-item">
                    <label>密码:</label>
                    <input type="password" id="assist-password" placeholder="请输入密码">
                </div>
                <div class="miit-form-item">
                    <label>短信验证码:</label>
                    <div class="miit-input-group">
                        <input type="text" id="assist-sms-code" placeholder="验证码">
                        <button id="get-sms-btn" class="miit-btn miit-btn-success">获取验证码</button>
                    </div>
                </div>
                <div class="miit-button-group">
                    <button id="fill-form-btn" class="miit-btn miit-btn-primary">填充表单</button>
                    <button id="auto-login-btn" class="miit-btn miit-btn-warning">自动登录</button>
                </div>
                <div class="miit-form-item">
                    <button id="highlight-btn" class="miit-btn miit-btn-info miit-btn-block">🎯 高亮显示表单元素</button>
                </div>
                <div id="assist-status">
                    ✅ 助手已就绪，请填写登录信息
                </div>
            </div>
        </div>
    `;

    // 注入隔离样式
    injectAssistantStyles();

    document.body.appendChild(assistant);
    bindAssistantEvents();

    // 监听样式变化，防止被原网页覆盖
    observeAssistantStyles(assistant);
}

// 绑定助手事件
function bindAssistantEvents() {
    const statusDiv = document.getElementById('assist-status');
    
    const setStatus = (message, type = 'info') => {
        const icons = {
            info: 'ℹ️',
            success: '✅',
            error: '❌',
            warning: '⚠️',
            loading: '⏳'
        };
        
        const colors = {
            info: '#409eff',
            success: '#67c23a',
            error: '#f56c6c',
            warning: '#e6a23c',
            loading: '#409eff'
        };
        
        statusDiv.innerHTML = `${icons[type]} ${message}`;
        statusDiv.style.borderLeftColor = colors[type];
        statusDiv.style.color = colors[type];
    };

    // 关闭助手
    document.getElementById('close-assistant').onclick = () => {
        document.getElementById('miit-login-assistant').remove();
    };

    // 获取验证码
    document.getElementById('get-sms-btn').onclick = async () => {
        const username = document.getElementById('assist-username').value.trim();
        const password = document.getElementById('assist-password').value.trim();
        
        if (!username || !password) {
            setStatus('请先输入用户名和密码', 'warning');
            return;
        }
        
        setStatus('正在获取验证码...', 'loading');
        
        try {
            // 填充表单
            fillPageForm(username, password, '');
            
            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 点击获取验证码按钮
            const getSmsButton = document.getElementById('btn');
            if (getSmsButton && !getSmsButton.disabled) {
                getSmsButton.click();
                setStatus('已触发获取验证码，请查看页面提示和手机短信', 'success');
            } else {
                setStatus('获取验证码按钮不可用，请检查用户名和密码', 'error');
            }
        } catch (error) {
            console.error('获取验证码失败:', error);
            setStatus('获取验证码失败: ' + error.message, 'error');
        }
    };

    // 填充表单
    document.getElementById('fill-form-btn').onclick = () => {
        const username = document.getElementById('assist-username').value.trim();
        const password = document.getElementById('assist-password').value.trim();
        const smsCode = document.getElementById('assist-sms-code').value.trim();
        
        const success = fillPageForm(username, password, smsCode);
        
        if (success) {
            setStatus('表单填充成功', 'success');
        } else {
            setStatus('表单填充失败，请检查页面元素', 'error');
        }
    };

    // 自动登录
    document.getElementById('auto-login-btn').onclick = async () => {
        const username = document.getElementById('assist-username').value.trim();
        const password = document.getElementById('assist-password').value.trim();
        const smsCode = document.getElementById('assist-sms-code').value.trim();
        
        if (!username || !password || !smsCode) {
            setStatus('请填写完整的登录信息', 'warning');
            return;
        }
        
        setStatus('正在执行登录...', 'loading');
        
        try {
            // 填充表单
            fillPageForm(username, password, smsCode);
            
            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 点击登录按钮
            const loginButton = document.getElementById('subbtn');
            if (loginButton && !loginButton.disabled) {
                loginButton.click();
                setStatus('登录请求已发送，请等待页面响应', 'success');
                
                // 监听页面变化
                watchForLoginResult((result) => {
                    if (result.success) {
                        setStatus('登录成功！页面即将跳转', 'success');
                    } else {
                        setStatus('登录失败: ' + result.message, 'error');
                    }
                });
            } else {
                setStatus('登录按钮不可用，请检查表单信息', 'error');
            }
        } catch (error) {
            console.error('自动登录失败:', error);
            setStatus('自动登录失败: ' + error.message, 'error');
        }
    };

    // 高亮元素
    document.getElementById('highlight-btn').onclick = () => {
        highlightLoginElements();
        setStatus('已高亮显示表单元素（3秒）', 'info');
    };

    // 同步表单值
    const syncFormValues = () => {
        const pageUsername = document.getElementById('name')?.value || '';
        const pagePassword = document.getElementById('password')?.value || '';
        const pageSmsCode = document.getElementById('yznum')?.value || '';
        
        if (pageUsername && !document.getElementById('assist-username').value) {
            document.getElementById('assist-username').value = pageUsername;
        }
        if (pagePassword && !document.getElementById('assist-password').value) {
            document.getElementById('assist-password').value = pagePassword;
        }
        if (pageSmsCode && !document.getElementById('assist-sms-code').value) {
            document.getElementById('assist-sms-code').value = pageSmsCode;
        }
    };

    // 定期同步
    setInterval(syncFormValues, 2000);
    syncFormValues();
}

// 填充页面表单
function fillPageForm(username, password, smsCode) {
    let success = true;
    
    try {
        if (username) {
            const usernameInput = document.getElementById('name');
            if (usernameInput) {
                usernameInput.value = username;
                usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
                usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                success = false;
            }
        }
        
        if (password) {
            const passwordInput = document.getElementById('password');
            if (passwordInput) {
                passwordInput.value = password;
                passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
                passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                success = false;
            }
        }
        
        if (smsCode) {
            const smsCodeInput = document.getElementById('yznum');
            if (smsCodeInput) {
                smsCodeInput.value = smsCode;
                smsCodeInput.dispatchEvent(new Event('input', { bubbles: true }));
                smsCodeInput.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                success = false;
            }
        }
    } catch (error) {
        console.error('填充表单失败:', error);
        success = false;
    }
    
    return success;
}

// 高亮登录元素
function highlightLoginElements() {
    const selectors = ['#name', '#password', '#yznum', '#btn', '#subbtn'];
    const elements = selectors.map(selector => document.querySelector(selector)).filter(el => el);

    elements.forEach(element => {
        const originalStyle = element.style.cssText;
        element.style.cssText += '; border: 2px solid #ff4444 !important; box-shadow: 0 0 10px #ff4444 !important; background-color: rgba(255, 68, 68, 0.1) !important;';
        
        setTimeout(() => {
            element.style.cssText = originalStyle;
        }, 3000);
    });
}

// 监听登录结果
function watchForLoginResult(callback) {
    const originalUrl = window.location.href;
    let checkCount = 0;
    const maxChecks = 30; // 30秒超时
    
    const checkInterval = setInterval(() => {
        checkCount++;
        const currentUrl = window.location.href;
        
        // 检查是否超时
        if (checkCount >= maxChecks) {
            clearInterval(checkInterval);
            callback({
                success: false,
                message: '登录超时',
                timeout: true
            });
            return;
        }
        
        // 检查URL是否发生变化（登录成功通常会跳转）
        if (currentUrl !== originalUrl) {
            clearInterval(checkInterval);
            callback({
                success: true,
                message: '登录成功，页面已跳转',
                newUrl: currentUrl
            });
            return;
        }
        
        // 检查页面是否有错误提示
        const errorSelectors = [
            '.layui-layer-content',
            '.error-message', 
            '.alert',
            '[class*="error"]',
            '[class*="fail"]'
        ];
        
        for (const selector of errorSelectors) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                const text = element.textContent?.trim();
                if (text && (text.includes('错误') || text.includes('失败') || 
                           text.includes('验证码') || text.includes('密码'))) {
                    clearInterval(checkInterval);
                    callback({
                        success: false,
                        message: text,
                        error: true
                    });
                    return;
                }
            }
        }
    }, 1000);
}

/**
 * 注入助手样式（样式隔离版本）
 */
function injectAssistantStyles() {
    // 检查是否已经注入
    if (document.getElementById('miit-assistant-isolated-styles')) {
        return;
    }

    const style = document.createElement('style');
    style.id = 'miit-assistant-isolated-styles';
    style.textContent = `
        /* 工信部登录助手样式隔离 */
        #miit-login-assistant {
            /* 强制重置所有可能被继承的样式 */
            all: initial !important;

            /* 重新设置容器样式 */
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 320px !important;
            z-index: 2147483647 !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            color: #333333 !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            background: transparent !important;
            box-sizing: border-box !important;
        }

        /* 确保助手内的所有元素不受原网页样式影响 */
        #miit-login-assistant *,
        #miit-login-assistant *::before,
        #miit-login-assistant *::after {
            all: unset !important;
            display: revert !important;
            box-sizing: border-box !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }

        /* 重新定义必要的元素样式 */
        #miit-login-assistant div {
            display: block !important;
        }

        #miit-login-assistant span {
            display: inline !important;
        }

        #miit-login-assistant button {
            display: inline-block !important;
            cursor: pointer !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 8px 16px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            text-align: center !important;
            text-decoration: none !important;
            transition: all 0.2s ease !important;
            user-select: none !important;
        }

        #miit-login-assistant input {
            display: block !important;
            width: 100% !important;
            padding: 10px !important;
            border: 1px solid #dcdfe6 !important;
            border-radius: 4px !important;
            font-size: 14px !important;
            background-color: #ffffff !important;
            color: #333333 !important;
            transition: border-color 0.2s ease !important;
        }

        #miit-login-assistant input:focus {
            border-color: #409eff !important;
            outline: none !important;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
        }

        #miit-login-assistant input::placeholder {
            color: #c0c4cc !important;
        }

        #miit-login-assistant label {
            display: block !important;
            margin-bottom: 4px !important;
            font-size: 13px !important;
            color: #666666 !important;
            font-weight: 500 !important;
        }

        /* 按钮样式 */
        #miit-login-assistant button:hover {
            opacity: 0.9 !important;
            transform: translateY(-1px) !important;
        }

        #miit-login-assistant button:active {
            transform: translateY(0) !important;
        }

        #miit-login-assistant #close-assistant {
            background: none !important;
            color: white !important;
            font-size: 20px !important;
            width: 24px !important;
            height: 24px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
        }

        #miit-login-assistant #close-assistant:hover {
            background-color: rgba(255, 255, 255, 0.2) !important;
        }

        /* 主容器样式保护 */
        #miit-login-assistant .miit-assistant-main {
            position: relative !important;
            width: 100% !important;
            background: #ffffff !important;
            border: 2px solid #409eff !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
            overflow: hidden !important;
        }

        /* 头部样式保护 */
        #miit-login-assistant .miit-assistant-header {
            background: linear-gradient(135deg, #409eff, #67c23a) !important;
            color: #ffffff !important;
            padding: 12px 16px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            font-weight: bold !important;
        }

        /* 内容区域样式 */
        #miit-login-assistant .miit-assistant-content {
            padding: 16px !important;
            background: #ffffff !important;
        }

        /* 状态区域样式 */
        #miit-login-assistant #assist-status {
            font-size: 12px !important;
            background: #f5f7fa !important;
            padding: 10px !important;
            border-radius: 4px !important;
            border-left: 3px solid #409eff !important;
            min-height: 20px !important;
            line-height: 1.4 !important;
            margin: 0 !important;
        }

        /* 表单项样式 */
        #miit-login-assistant .miit-form-item {
            margin-bottom: 12px !important;
            display: block !important;
        }

        /* 输入组样式 */
        #miit-login-assistant .miit-input-group {
            display: flex !important;
            gap: 8px !important;
            align-items: center !important;
        }

        #miit-login-assistant .miit-input-group input {
            flex: 1 !important;
        }

        /* 按钮组样式 */
        #miit-login-assistant .miit-button-group {
            display: flex !important;
            gap: 8px !important;
            margin-bottom: 12px !important;
        }

        #miit-login-assistant .miit-button-group button {
            flex: 1 !important;
        }

        /* 按钮样式类 */
        #miit-login-assistant .miit-btn {
            display: inline-block !important;
            padding: 10px 16px !important;
            border: none !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            text-align: center !important;
            transition: all 0.2s ease !important;
            user-select: none !important;
        }

        #miit-login-assistant .miit-btn-primary {
            background: #409eff !important;
            color: #ffffff !important;
        }

        #miit-login-assistant .miit-btn-success {
            background: #67c23a !important;
            color: #ffffff !important;
            padding: 10px 16px !important;
            white-space: nowrap !important;
        }

        #miit-login-assistant .miit-btn-warning {
            background: #e6a23c !important;
            color: #ffffff !important;
        }

        #miit-login-assistant .miit-btn-info {
            background: #909399 !important;
            color: #ffffff !important;
        }

        #miit-login-assistant .miit-btn-block {
            width: 100% !important;
            display: block !important;
        }

        /* 防止被原网页的CSS框架影响 */
        #miit-login-assistant .miit-assistant-main,
        #miit-login-assistant .miit-assistant-main * {
            /* Bootstrap重置 */
            -webkit-box-sizing: border-box !important;
            -moz-box-sizing: border-box !important;
            box-sizing: border-box !important;

            /* Foundation重置 */
            margin: revert !important;
            padding: revert !important;

            /* Tailwind重置 */
            border-width: revert !important;
            border-style: revert !important;
            border-color: revert !important;
        }
    `;

    // 插入到head的最后，确保优先级
    document.head.appendChild(style);
}

/**
 * 监听助手样式变化，防止被原网页覆盖
 */
function observeAssistantStyles(assistant) {
    if (!window.MutationObserver) {
        return;
    }

    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' &&
                (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
                // 如果助手样式被修改，重新应用隔离样式
                if (mutation.target === assistant || assistant.contains(mutation.target)) {
                    console.warn('🔐 检测到助手样式被修改，重新应用保护样式');
                    reapplyAssistantStyles(assistant);
                }
            }
        });
    });

    observer.observe(assistant, {
        attributes: true,
        attributeFilter: ['style', 'class'],
        subtree: true
    });

    // 定期检查助手是否还在DOM中
    const checkInterval = setInterval(() => {
        if (!document.body.contains(assistant)) {
            observer.disconnect();
            clearInterval(checkInterval);
            console.log('🔐 助手已从DOM中移除，停止样式监听');
        }
    }, 5000);
}

/**
 * 重新应用助手样式
 */
function reapplyAssistantStyles(assistant) {
    assistant.style.cssText = `
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        width: 320px !important;
        z-index: 2147483647 !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        background: transparent !important;
        box-sizing: border-box !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
        color: #333 !important;
        all: initial !important;
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        width: 320px !important;
        z-index: 2147483647 !important;
    `;
}

// 添加样式（保留原有函数名以兼容）
function addStyles() {
    injectAssistantStyles();
}

// 主函数
async function main() {
    if (!isMiitLoginPage()) {
        return;
    }
    
    console.log('🔐 工信部登录助手已加载');
    
    // 等待页面加载完成
    await waitForPageReady();
    
    // 等待表单元素加载
    let retryCount = 0;
    const maxRetries = 10;
    
    while (retryCount < maxRetries) {
        const form = document.getElementById('login-form');
        if (form) {
            break;
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
        retryCount++;
    }
    
    if (retryCount >= maxRetries) {
        console.error('登录表单未找到，助手无法启动');
        return;
    }
    
    // 添加样式
    addStyles();
    
    // 创建登录助手
    createLoginAssistant();
    
    console.log('✅ 工信部登录助手已启动');
}

// 启动
main().catch(console.error);
