/**
 * 工信部登录 - Chrome扩展内容脚本
 * 在工信部登录页面注入登录助手
 */

// 检查是否为工信部登录页面
function isMiitLoginPage() {
    return window.location.href.includes('ucenter.miit.gov.cn') && 
           window.location.pathname.includes('login.jsp');
}

// 等待页面加载完成
function waitForPageReady() {
    return new Promise((resolve) => {
        if (document.readyState === 'complete') {
            resolve();
        } else {
            window.addEventListener('load', resolve);
        }
    });
}

// 创建登录助手界面
function createLoginAssistant() {
    // 检查是否已存在
    if (document.getElementById('miit-login-assistant')) {
        return;
    }

    const assistant = document.createElement('div');
    assistant.id = 'miit-login-assistant';
    assistant.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            width: 320px;
            background: white;
            border: 2px solid #409eff;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
            <div style="
                background: linear-gradient(135deg, #409eff, #67c23a);
                color: white;
                padding: 12px 16px;
                border-radius: 6px 6px 0 0;
                font-weight: bold;
                display: flex;
                justify-content: space-between;
                align-items: center;
            ">
                <span>🔐 工信部登录助手</span>
                <button id="close-assistant" style="
                    background: none;
                    border: none;
                    color: white;
                    font-size: 20px;
                    cursor: pointer;
                    padding: 0;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">×</button>
            </div>
            <div style="padding: 16px;">
                <div style="margin-bottom: 12px;">
                    <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #666; font-weight: 500;">用户名:</label>
                    <input type="text" id="assist-username" placeholder="登录名/统一社会信用代码/身份证号" style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #dcdfe6;
                        border-radius: 4px;
                        font-size: 14px;
                        box-sizing: border-box;
                        transition: border-color 0.2s;
                    ">
                </div>
                <div style="margin-bottom: 12px;">
                    <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #666; font-weight: 500;">密码:</label>
                    <input type="password" id="assist-password" placeholder="请输入密码" style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #dcdfe6;
                        border-radius: 4px;
                        font-size: 14px;
                        box-sizing: border-box;
                        transition: border-color 0.2s;
                    ">
                </div>
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #666; font-weight: 500;">短信验证码:</label>
                    <div style="display: flex; gap: 8px;">
                        <input type="text" id="assist-sms-code" placeholder="验证码" style="
                            flex: 1;
                            padding: 10px;
                            border: 1px solid #dcdfe6;
                            border-radius: 4px;
                            font-size: 14px;
                            transition: border-color 0.2s;
                        ">
                        <button id="get-sms-btn" style="
                            padding: 10px 16px;
                            background: #67c23a;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 13px;
                            white-space: nowrap;
                            transition: background-color 0.2s;
                        ">获取验证码</button>
                    </div>
                </div>
                <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                    <button id="fill-form-btn" style="
                        flex: 1;
                        padding: 12px;
                        background: #409eff;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: background-color 0.2s;
                    ">填充表单</button>
                    <button id="auto-login-btn" style="
                        flex: 1;
                        padding: 12px;
                        background: #e6a23c;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: background-color 0.2s;
                    ">自动登录</button>
                </div>
                <div style="margin-bottom: 12px;">
                    <button id="highlight-btn" style="
                        width: 100%;
                        padding: 10px;
                        background: #909399;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 13px;
                        transition: background-color 0.2s;
                    ">🎯 高亮显示表单元素</button>
                </div>
                <div id="assist-status" style="
                    font-size: 12px;
                    color: #666;
                    background: #f5f7fa;
                    padding: 10px;
                    border-radius: 4px;
                    border-left: 3px solid #409eff;
                    min-height: 20px;
                    line-height: 1.4;
                ">
                    ✅ 助手已就绪，请填写登录信息
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(assistant);
    bindAssistantEvents();
}

// 绑定助手事件
function bindAssistantEvents() {
    const statusDiv = document.getElementById('assist-status');
    
    const setStatus = (message, type = 'info') => {
        const icons = {
            info: 'ℹ️',
            success: '✅',
            error: '❌',
            warning: '⚠️',
            loading: '⏳'
        };
        
        const colors = {
            info: '#409eff',
            success: '#67c23a',
            error: '#f56c6c',
            warning: '#e6a23c',
            loading: '#409eff'
        };
        
        statusDiv.innerHTML = `${icons[type]} ${message}`;
        statusDiv.style.borderLeftColor = colors[type];
        statusDiv.style.color = colors[type];
    };

    // 关闭助手
    document.getElementById('close-assistant').onclick = () => {
        document.getElementById('miit-login-assistant').remove();
    };

    // 获取验证码
    document.getElementById('get-sms-btn').onclick = async () => {
        const username = document.getElementById('assist-username').value.trim();
        const password = document.getElementById('assist-password').value.trim();
        
        if (!username || !password) {
            setStatus('请先输入用户名和密码', 'warning');
            return;
        }
        
        setStatus('正在获取验证码...', 'loading');
        
        try {
            // 填充表单
            fillPageForm(username, password, '');
            
            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 点击获取验证码按钮
            const getSmsButton = document.getElementById('btn');
            if (getSmsButton && !getSmsButton.disabled) {
                getSmsButton.click();
                setStatus('已触发获取验证码，请查看页面提示和手机短信', 'success');
            } else {
                setStatus('获取验证码按钮不可用，请检查用户名和密码', 'error');
            }
        } catch (error) {
            console.error('获取验证码失败:', error);
            setStatus('获取验证码失败: ' + error.message, 'error');
        }
    };

    // 填充表单
    document.getElementById('fill-form-btn').onclick = () => {
        const username = document.getElementById('assist-username').value.trim();
        const password = document.getElementById('assist-password').value.trim();
        const smsCode = document.getElementById('assist-sms-code').value.trim();
        
        const success = fillPageForm(username, password, smsCode);
        
        if (success) {
            setStatus('表单填充成功', 'success');
        } else {
            setStatus('表单填充失败，请检查页面元素', 'error');
        }
    };

    // 自动登录
    document.getElementById('auto-login-btn').onclick = async () => {
        const username = document.getElementById('assist-username').value.trim();
        const password = document.getElementById('assist-password').value.trim();
        const smsCode = document.getElementById('assist-sms-code').value.trim();
        
        if (!username || !password || !smsCode) {
            setStatus('请填写完整的登录信息', 'warning');
            return;
        }
        
        setStatus('正在执行登录...', 'loading');
        
        try {
            // 填充表单
            fillPageForm(username, password, smsCode);
            
            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 点击登录按钮
            const loginButton = document.getElementById('subbtn');
            if (loginButton && !loginButton.disabled) {
                loginButton.click();
                setStatus('登录请求已发送，请等待页面响应', 'success');
                
                // 监听页面变化
                watchForLoginResult((result) => {
                    if (result.success) {
                        setStatus('登录成功！页面即将跳转', 'success');
                    } else {
                        setStatus('登录失败: ' + result.message, 'error');
                    }
                });
            } else {
                setStatus('登录按钮不可用，请检查表单信息', 'error');
            }
        } catch (error) {
            console.error('自动登录失败:', error);
            setStatus('自动登录失败: ' + error.message, 'error');
        }
    };

    // 高亮元素
    document.getElementById('highlight-btn').onclick = () => {
        highlightLoginElements();
        setStatus('已高亮显示表单元素（3秒）', 'info');
    };

    // 同步表单值
    const syncFormValues = () => {
        const pageUsername = document.getElementById('name')?.value || '';
        const pagePassword = document.getElementById('password')?.value || '';
        const pageSmsCode = document.getElementById('yznum')?.value || '';
        
        if (pageUsername && !document.getElementById('assist-username').value) {
            document.getElementById('assist-username').value = pageUsername;
        }
        if (pagePassword && !document.getElementById('assist-password').value) {
            document.getElementById('assist-password').value = pagePassword;
        }
        if (pageSmsCode && !document.getElementById('assist-sms-code').value) {
            document.getElementById('assist-sms-code').value = pageSmsCode;
        }
    };

    // 定期同步
    setInterval(syncFormValues, 2000);
    syncFormValues();
}

// 填充页面表单
function fillPageForm(username, password, smsCode) {
    let success = true;
    
    try {
        if (username) {
            const usernameInput = document.getElementById('name');
            if (usernameInput) {
                usernameInput.value = username;
                usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
                usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                success = false;
            }
        }
        
        if (password) {
            const passwordInput = document.getElementById('password');
            if (passwordInput) {
                passwordInput.value = password;
                passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
                passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                success = false;
            }
        }
        
        if (smsCode) {
            const smsCodeInput = document.getElementById('yznum');
            if (smsCodeInput) {
                smsCodeInput.value = smsCode;
                smsCodeInput.dispatchEvent(new Event('input', { bubbles: true }));
                smsCodeInput.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                success = false;
            }
        }
    } catch (error) {
        console.error('填充表单失败:', error);
        success = false;
    }
    
    return success;
}

// 高亮登录元素
function highlightLoginElements() {
    const selectors = ['#name', '#password', '#yznum', '#btn', '#subbtn'];
    const elements = selectors.map(selector => document.querySelector(selector)).filter(el => el);

    elements.forEach(element => {
        const originalStyle = element.style.cssText;
        element.style.cssText += '; border: 2px solid #ff4444 !important; box-shadow: 0 0 10px #ff4444 !important; background-color: rgba(255, 68, 68, 0.1) !important;';
        
        setTimeout(() => {
            element.style.cssText = originalStyle;
        }, 3000);
    });
}

// 监听登录结果
function watchForLoginResult(callback) {
    const originalUrl = window.location.href;
    let checkCount = 0;
    const maxChecks = 30; // 30秒超时
    
    const checkInterval = setInterval(() => {
        checkCount++;
        const currentUrl = window.location.href;
        
        // 检查是否超时
        if (checkCount >= maxChecks) {
            clearInterval(checkInterval);
            callback({
                success: false,
                message: '登录超时',
                timeout: true
            });
            return;
        }
        
        // 检查URL是否发生变化（登录成功通常会跳转）
        if (currentUrl !== originalUrl) {
            clearInterval(checkInterval);
            callback({
                success: true,
                message: '登录成功，页面已跳转',
                newUrl: currentUrl
            });
            return;
        }
        
        // 检查页面是否有错误提示
        const errorSelectors = [
            '.layui-layer-content',
            '.error-message', 
            '.alert',
            '[class*="error"]',
            '[class*="fail"]'
        ];
        
        for (const selector of errorSelectors) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                const text = element.textContent?.trim();
                if (text && (text.includes('错误') || text.includes('失败') || 
                           text.includes('验证码') || text.includes('密码'))) {
                    clearInterval(checkInterval);
                    callback({
                        success: false,
                        message: text,
                        error: true
                    });
                    return;
                }
            }
        }
    }, 1000);
}

// 添加样式
function addStyles() {
    const style = document.createElement('style');
    style.textContent = `
        #miit-login-assistant input:focus {
            border-color: #409eff !important;
            outline: none !important;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
        }
        
        #miit-login-assistant button:hover {
            opacity: 0.9 !important;
            transform: translateY(-1px) !important;
        }
        
        #miit-login-assistant button:active {
            transform: translateY(0) !important;
        }
        
        #close-assistant:hover {
            background-color: rgba(255, 255, 255, 0.2) !important;
        }
    `;
    document.head.appendChild(style);
}

// 主函数
async function main() {
    if (!isMiitLoginPage()) {
        return;
    }
    
    console.log('🔐 工信部登录助手已加载');
    
    // 等待页面加载完成
    await waitForPageReady();
    
    // 等待表单元素加载
    let retryCount = 0;
    const maxRetries = 10;
    
    while (retryCount < maxRetries) {
        const form = document.getElementById('login-form');
        if (form) {
            break;
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
        retryCount++;
    }
    
    if (retryCount >= maxRetries) {
        console.error('登录表单未找到，助手无法启动');
        return;
    }
    
    // 添加样式
    addStyles();
    
    // 创建登录助手
    createLoginAssistant();
    
    console.log('✅ 工信部登录助手已启动');
}

// 启动
main().catch(console.error);
