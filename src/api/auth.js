// auth.js
import axios from "axios"

// 从Cookie获取JWT Token
export const getAuthToken = () => {
    const cookie = document.cookie.match(/user=([^;]+)/)
    return cookie?.[1] ? `Bearer ${cookie[1]}` : null
}

// 封装带认证的API请求
export const callAuthApi = (config) => {
    const authToken = getAuthToken()
    return axios({
        ...config,
        headers: {
            ...config.headers,
            Authorization: authToken,
            "Content-Type": "application/json",
            isajax: "true"
        },
        withCredentials: true,
        showLoading: true, // ✅ 添加加载提示配置
        stepMessage: '校验中，请稍后' // ✅ 自定义提示文案
    })
}
