// auth.js
import axios from "axios"
const SSO_LOGIN_URL = "http://cpms.hq.cmcc/pms/ewps/api/auth/login/sso";

// 从Cookie获取JWT Token
export const getAuthToken = () => {
    const cookie = document.cookie.match(/user=([^;]+)/)
    return cookie?.[1] ? `Bearer ${cookie[1]}` : null
}
export const getInmsToken = async () => {
    // 若 cookie 中没有 inmsToken，则尝试获取 user cookie 并调用 singleLogin 接口
    const cookieMatch = document.cookie.match(/user=([^;]+)/);
    const userToken = cookieMatch?.[1];

    if (!userToken) {
        console.warn("未找到 user token，无法进行授权");
        return null;
    }

    try {
        const response = await axios.post(SSO_LOGIN_URL, {
            token: userToken
        }, axiosConfig);

        const data = response.data;

        // 增加防御性判断避免运行时错误
        if (data && data.body && data.body.token) {
            console.warn("token",data.body.token);
            return data.body.token;
        } else {
            console.warn("授权失败，响应中无有效 token");
            return null;
        }
    } catch (error) {
        console.error("调用 singleLogin 接口失败", error.message || error);
        return null;
    }
};

const axiosConfig = {
    loading: true,
    withCredentials: true
};




// 封装带认证的API请求
export const callAuthApi = (config) => {
    const authToken = getAuthToken()
    return axios({
        ...config,
        headers: {
            ...config.headers,
            Authorization: authToken,
            "Content-Type": "application/json",
            isajax: "true"
        },
        withCredentials: true,
        showLoading: true, // ✅ 添加加载提示配置
        stepMessage: '校验中，请稍后' // ✅ 自定义提示文案
    })
}
