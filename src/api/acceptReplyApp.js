import {callAuth<PERSON>pi} from './auth';
import axios from "axios";

const API_BASE_URL = process.env.VUE_APP_API_URL;

// 查询结算审核列表
export function getSettlementAuditsList(param) {
    return callAuthApi({
        url: window.location.origin + '/cpms/maut/v1/auditSettle/list',
        method: "post",
        data: param,
    });
}

// 获取PMS施工费
export function getConstructionFee(settleId) {
    return callAuthApi({
        url: window.location.origin + '/cpms/maut/v1/taskMessage/getMoneyOverView?settleId=' + settleId,
        method: "get",
    });
}

// 比较三费信息
export function freeValidation(data) {
    return axios.request( {
        url: `${API_BASE_URL}/provinceConstructionAudit/threeFeeVerification`,
        method: 'POST',
        data: data
    })
}


