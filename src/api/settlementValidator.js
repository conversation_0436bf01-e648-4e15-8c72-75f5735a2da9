import pLimit from 'p-limit';
import {freeValidation, getConstructionFee, getSettlementAuditsList} from '@/api/acceptReplyApp';
import {callApiEpms, logValidation, postApiEpms} from "@/api/commonApi";
import { EamLoading } from "@/util/EamElementExt"
import { Loading, Message, MessageBox } from 'element-ui';

export async function fetchSettlementFees(projectCode) {
    let requestParams = null;
    let responseData = null;
    let errorMessages = null;
    let validationResult = false;

    async function logFeeValidation(settleId, isSuccessful, requestParams = null, responseData = null, errorMessages = null) {
        await logValidation({
            businessType: 'CONSTRUCTION_FEE',
            projectCode: projectCode,
            taskCodes: settleId,
            validationResults: isSuccessful ? '成功' : '失败',
            requestParams: JSON.stringify(requestParams),
            responseData: JSON.stringify(responseData),
            isSuccessful: isSuccessful,
            errorMessages: errorMessages,
            pageUrl: window.location.href
        });
    }

    try {
        EamLoading.service();
        const baseQuery = {
            page: 1,
            size: 500,
            companyCode: "",
            deleteFlag: 0,
            selectDefault: "",
            auditsettleStatuss: ["审批完成"],
            projectCodeLike: projectCode,
            sysDataAuthQueryDetailVo: {key: ""},
            value: ""
        };
        requestParams = baseQuery;

        const listResponse = await getSettlementAuditsList(baseQuery);
        const {list} = listResponse?.data?.body || {};
        responseData = listResponse;

        if (!Array.isArray(list) || list.length === 0) {
            const errorMessage = `未找到相关结算数据 for projectCode: ${projectCode}`;
            // 抛出异常以触发 catch 块
            throw new Error(errorMessage);
        }

        const limit = pLimit(5);
        const feePromises = list.map(item =>
            limit(async () => {
                let res = null;
                try {
                    res = await getConstructionFee(item.settleId);
                    const {body} = res?.data || {};
                    const {settleMoney, taskMsgMoney} = body || {};

                    await logFeeValidation(item.settleId, true, {settleId: item.settleId}, res);

                    if (!settleMoney?.[0] || !taskMsgMoney?.[0]) {
                        const errorMessage = `费用数据不完整 for projectCode: ${projectCode} and settleId ` + item.settleId;
                        // 抛出异常以触发 catch 块
                        throw new Error(errorMessage);
                    }

                    return {
                        projectCode,
                        vendorCode: settleMoney[0].vendorCode,
                        vendorName: settleMoney[0].vendorName,
                        constructionTotalFee: settleMoney[0].validatedMoney + taskMsgMoney[0].validatedMoney - taskMsgMoney[0].approvalMoney,
                        buildDeptName: item.buildDeptName,
                        buildDept: item.buildDept,
                    };
                } catch (error) {
                    plugin.error(`[${new Date().toISOString()}] 计算施工费用失败`, { settleId: item.settleId, error });
                    await logFeeValidation(item.settleId, false, { settleId: item.settleId }, res, error.message);
                    return null;
                }
            })
        );

        const results = await Promise.all(feePromises);
        const validResults = results.filter(Boolean);

        const mergedResults = validResults.reduce((acc, curr) => {
            const key = `${curr.projectCode}-${curr.vendorCode}-${curr.buildDept}`;
            const existing = acc.find(item => item.key === key);

            if (existing) {
                existing.constructionTotalFee += curr.constructionTotalFee;
            } else {
                acc.push({ ...curr, key });
            }
            return acc;
        }, []);
        await freeValidation(mergedResults).then(data => {
            plugin.log('合并后的结算数据:', data);
            const result = data.data;
            result.ok = validationResult;
            if (result.ok) {
                Message.success(result.msg)
            } else {
                MessageBox.alert(result.msg, '提示', {
                    confirmButtonText: '确定',
                    type: 'warning'
                });
            }
        })
    } catch (error) {
        plugin.error(`[${new Date().toISOString()}] 获取结算费用数据失败`, { projectCode, error });
        errorMessages = "获取结算费用列表数据失败：" + error.message;
        Message.error(errorMessages);
    } finally {
        try {
            await logValidation({
                businessType: 'ACCEPT_REPLY',
                projectCode,
                validationResults: validationResult,
                requestParams: JSON.stringify(requestParams),
                responseData: JSON.stringify(responseData),
                isSuccessful: validationResult,
                errorMessages,
                pageUrl: window.location.href
            });
        } catch (logError) {
            plugin.error('记录主流程日志失败:', logError);
        }
        EamLoading.close();
    }
    return validationResult;
}


export function query(projectCode) {
    return callApiEpms(`/groupDwepTaskBalanceAction/checkMessage?productId=${projectCode}`)
}



export function fetchBalance(param) {
    return postApiEpms( '/groupDwepTaskBalanceAction/sendTodo', param);
}


export function checkx(projectCode) {
    return callApiEpms( `/groupDwepTaskBalanceAction/checkBalances?productId=${projectCode}`);
}

export function checkWz(param) {
    return postApiEpms( '/materials/remoteCheck', param);
}

export function checkTime(codeValue) {
    return callApiEpms( `/materials/checkTime?codeValue=${codeValue}`);
}

