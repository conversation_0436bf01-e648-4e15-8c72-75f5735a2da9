
/**
 * 临时移除事件监听器，触发原生事件后再延迟重新绑定
 * @param {Element} el - 目标元素
 * @param {string} event - 事件类型（如 'click'）
 * @param {Function} handler - 事件处理函数
 * @param {Object} options - 事件监听选项（如 { capture: true }）
 * @param {number} delay - 重新绑定的延迟时间（毫秒，默认1000ms）
 */
export function executeOriginalLogic(el, handler, event = 'click', options = { capture: true }, delay = 100) {
    if (!el) return;
    el.removeEventListener(event, handler, options);

    // 触发原生事件
    const evt = new MouseEvent(event, {
        bubbles: true,
        cancelable: true,
        view: window
    });
    evt.__fromPlugin = true;
    el.dispatchEvent(evt);

    // 延迟重新绑定
    setTimeout(() => {
        el.addEventListener(event, handler, options);
    }, delay);
}


/**
 * 为目标元素绑定插件事件监听（防止重复绑定）
 * @param {Element} el - 目标元素
 * @param {string} event - 事件类型（如 'click'）
 * @param {Function} handler - 事件处理函数
 * @param {Object} options - 事件监听选项 默认 '{ capture: true }'
 * @param {string} flag - 标记属性名，默认 '__hasPluginListener'
 */
export function bindPluginEventListener(el, handler, event = 'click', options = { capture: true }, flag = '__hasPluginListener') {
    if (!el || el[flag]) return;
    el.addEventListener(event, handler, options);
    el[flag] = true;
}