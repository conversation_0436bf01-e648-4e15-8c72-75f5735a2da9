import { callAuthApi } from './auth';
import { postApiEpms } from './commonApi';

/**
 * 获取任务明细
 * @param {} param 
 * @returns 
 */
export function getTaskInfoById(param) {
    return callAuthApi({
        url: window.location.origin + '/cpms/mdsg/breakdownTaskSingle/v1/getTask',
        method: "get",
        params: param
    });
}

/**
 * 获取资源信息(资源与任务关联)的校验结果
 * @param {} param 
 * @returns 
 */
export function getResourceByValiResult(param) {
    return postApiEpms("/plugin/cutoverDimensionVali/resourceVali",param);
}