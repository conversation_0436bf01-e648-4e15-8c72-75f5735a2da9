import {callApiEpms, postApiEpms} from "@/api/commonApi";
import {callAuthApi} from "@/api/auth";

// 获取设计物资清单列表（设计产品维度）
export function queryReport(param, taskId, orgId) {
    return postApiEpms( '/taskDifference/syncData?taskId=' + taskId + '&orgId=' + orgId, param);
}

// 保存差异数据
export function add(data) {
    return postApiEpms("/taskDifference/save", data)
}

// 更新差异数据
export function update(data) {
    return postApiEpms("/taskDifference/update", data)
}

// 发送待办
export function sendTodo(data) {
    return postApiEpms("/dwepMaterialExpenseAlertTask/sendTodo", data)
}

export function checkPendingWorkOrderExists(data) {
    return postApiEpms("/dwepMaterialExpenseAlertTask/checkPendingWorkOrderExists", data)
}
export function getPayVerification(param) {
    return postApiEpms("/materials/checkQxMessage", param)
}
