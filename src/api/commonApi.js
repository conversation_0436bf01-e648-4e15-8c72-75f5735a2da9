import { getAuthToken,callAuthApi,getInmsToken } from './auth';
import {EamLoading} from "@/util/EamElementExt";

const API_BASE_URL = process.env.VUE_APP_API_URL;

/**
 * 通用 API 调用函数
 * @param {string} endpoint - API 端点路径，不包含基础 URL
 * @param {Object} options - 请求选项
 * @param {string} [options.method='GET'] - HTTP 请求方法
 * @param {Object} [options.headers] - 自定义请求头
 * @param {string} [options.body] - 请求体数据（已序列化的 JSON 字符串）
 * @param {string} [options.credentials='omit'] - 凭证策略
 * @returns {Promise<Object>} 返回解析后的 JSON 响应数据
 * @throws {Error} 当网络请求失败或响应状态码不是 2xx 时抛出错误
 * @example
 */
export async function callApiEpms(endpoint, options = {}) {
    const authToken = getAuthToken();
    let inmsToken = await getInmsToken();
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'VERSION': process.env.VUE_APP_API_VERSION,
            'authToken': authToken,
            'imsToken': inmsToken
        },
        credentials: 'omit'
    };

    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    return fetch(`${API_BASE_URL}${endpoint}`, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应异常');
            }
            return response.json();
        })
        .catch(error => {
            EamLoading.close();
            plugin.error('API调用错误:', error);
            throw error;
        });
}

/**
 * 基于 callApiEpms 的 POST 请求封装函数
 * @param {string} endpoint - API 端点路径
 * @param {Object} data - 请求体数据
 * @param {Object} options - 额外的请求选项
 * @returns {Promise} 返回 Promise 对象
 */
export function postApiEpms(endpoint, data, options = {}) {
    return callApiEpms(endpoint, {
        method: 'POST',
        body: JSON.stringify(data),
        ...options
    });
}

/**
 * 记录校验日志到数据库（公共方法）
 * @param {Object} logData - 参数对象
 * @param {string} logData.businessType - 插件业务（MMAT_TRANS_CREATE_APP 调拨校验）
 * @param {string} logData.projectCode - 项目编码
 * @param {string} logData.taskCodes - 任务编码
 * @param {String} logData.results - 校验结果
 * @param {String} [logData.requestlogData] - 请求参数
 * @param {String} [logData.responseData] - 接口返回数据
 * @param {String} [logData.isSuccessful] - 是否成功
 * @param {string} [logData.errorMessages] - 错误信息
 * @param {String} [logData.pageUrl] - 当前页面地址
 */
export function logValidation(logData) {
    // 获取登录信息
    const loginInfo = localStorage.getItem('loginInfo')
    if (loginInfo) {
        const decoded = JSON.parse(loginInfo)
        logData.userId = decoded.userId
        logData.userName = decoded.userName
        logData.email = decoded.email
        logData.groupName = decoded.groupName
        logData.loginInfo = loginInfo
    }
    return postApiEpms('/pluginValidationLogs/save', logData)
}

export function queryWhiteList(type, projectNo) {
    return callApiEpms(`/dwepWhiteListTable/queryWhiteList?type=${type}&projectNo=${projectNo}`)
}

/**
 * 获取当前公司列表
 * @returns
 */
export function getCurrentCompanyList() {
    return callAuthApi({
        url: window.location.origin + '/cpms/mcpt/transfer/api_v1/queryPopupData/queryCompanyAll',
        method: "get",
        params: {
            pageSize: 10,
            page: 1
        }
    });
}
