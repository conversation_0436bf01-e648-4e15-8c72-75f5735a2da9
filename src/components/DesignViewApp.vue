
<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 90px;top:115px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 10">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>

    <el-dialog
        title="验证失败 - 错误详情"
        :visible.sync="dialogVisible"
        width="920px" append-to-body class="eldialogIndex">
        <div style="height: 395px">
          <el-table :data="errorList" style="width: 100%" :header-cell-style="{
      'background-color': '#f9fcff','color': '#535861','font-weight': 'bold'}" v-loading="detailLoading">
            <el-table-column type="index" label="序号"  width="50" align="center"></el-table-column>
            <el-table-column prop="errorFile" label="错误文件名称" min-width="120" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="errorMessage" label="错误信息" min-width="100" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="type" label="类型" min-width="80" align="center" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <component v-for="item in componentList" :is="item.name" :key="item.key" :user-info="userInfo"></component>
  </div>
</template>
<script>
import { Message, MessageBox } from 'element-ui';

export default {
  name: 'App',
  components: {
  },
  data() {
    return {
      showTip: true,
      userInfo: null,
      loaded: false,
      componentList: [],
      allocationType: "",
      taskcode: "",
      projectCode: "",
      observer: null, // 用于存储 MutationObserver 实例
      allowSubmit: false,
      authToken: '' ,// 存储认证token
      taskId: null,  // 新增：用于存储 taskId
      loginNameElement: null,
      orderFormContent: null,
      spanValue: null,
      spanValue2:null,
      current: null,
      dialogVisible: false,
      errorList:[],
      detailLoading: false,
      isMounted: false,
      userID:""
    }
  },
  mounted() {
    this.$nextTick(()=>{

      this.extractParamsFromURL(); // 提取 URL 参数
        this.setupObserver();
      const observer = new MutationObserver(() => {
        if (document.querySelector('.login-name') && document.querySelector('.order_form_content')) {

          this.getLoginName();
          this.surveyCode = this.getFormFieldValue('设计勘察单号');
          this.projectCode = this.getFormFieldValue('项目编码');
          observer.disconnect(); // 找到后停止监听
        }
      });

      observer.observe(document.body, { childList: true, subtree: true });

    })

    // this.setupSubmitListenerWithObserver();

  },
  beforeUnmount() {
    // 组件销毁时，停止观察
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    setupObserver() {
      this.observer = new MutationObserver(() => {
        if (!this.isMounted) {
          this.checkAndAddValidationButton();
          this.isMounted = true;
          this.observer.disconnect();
        }
      });

      this.observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    },
    getFormFieldValue(labelText) {
      const labels = document.querySelectorAll('label.el-form-item__label');
      for (const label of labels) {
        if (label.textContent.trim().includes(labelText)) {
          const span = label.closest('.el-form-item').querySelector('.order_form_content span');
          if (span) {
            return span.textContent.trim();
          }
        }
      }
      return null;
    },

    retryGetElement(selector, callback, retries = 10, delay = 500) {
      const tryFind = () => {
        const el = document.querySelector(selector);
        if (el) {
          callback(el);
        } else if (retries > 0) {
          setTimeout(tryFind, delay);
        } else {
          plugin.warn(`❌ 未找到元素 ${selector}`);
        }
      };
      tryFind();
    },
    ensureElementsLoaded() {
      this.retryGetElement('.login-name', () => {
        this.getLoginName();
      });

      this.retryGetElement('.order_form_content', () => {
        this.getOrderFormContentSpans();
        this.getOrderFormSpans();
      });
    },
    getOrderFormContentSpans() {
      const orderFormContent = document.querySelector('.order_form_content');
      if (orderFormContent) {
        const spans = orderFormContent.querySelectorAll('span');
        if (spans.length > 0) {
          this.spanValue = spans.length > 0 ? spans[0].textContent.trim() : null;
          if (this.spanValue) {
            plugin.log('✅ 第一个 span 的值:', this.spanValue);
          }
          plugin.log('✅ .order_form_content 中的 span 值:', this.spanValues);
        } else {
          plugin.warn('⚠️ .order_form_content 中未找到 span 元素');
        }
      } else {
        plugin.warn('⚠️ 未找到 .order_form_content 元素');
      }
    },

    getOrderFormSpans() {
      const orderFormContent = document.querySelector('.order_form_content');
      if (orderFormContent) {
        const spans = orderFormContent.querySelectorAll('span');
        if (spans.length > 0) {
          this.spanValue2 = spans.length > 0 ? spans[0].textContent.trim() : null;
          if (this.spanValue2) {
            plugin.log('✅ 第一个 span 的值:', this.spanValue2);
          }
          plugin.log('✅ .order_form_content 中的 span 值:', this.spanValue2);
        } else {
          plugin.warn('⚠️ .order_form_content 中未找到 span 元素');
        }
      } else {
        plugin.warn('⚠️ 未找到 .order_form_content 元素');
      }
    },
    getLoginName() {
      const loginNameElement = document.querySelector('.login-name');
      if (loginNameElement) {
        this.loginNameValue = loginNameElement.textContent.trim();
        plugin.log('获取到 .login-name 的值:', this.loginNameValue);
        // 可以将值赋给 data 中的变量，例如：
        // this.userInfo = loginNameValue;
      } else {
        plugin.warn('未找到 .login-name 元素');
      }
    },

    extractParamsFromURL() {
      let username = localStorage.getItem('userInfo1');
      let userInfo = JSON.parse(username);
      this.userID = userInfo.userId;
      plugin.log("获取到 userID",  this.userID);
      const href = window.location.href;
      let search = '';

      // 判断是否有 hash，优先取 hash 中的内容
      if (href.includes('#')) {
        const hashPart = href.split('#')[1];
        if (hashPart.includes('?')) {
          search = hashPart.split('?')[1];
        }
      } else {
        // 没有 hash 的话取标准 search
        search = new URL(href).search;
      }

      const params = new URLSearchParams(search);

      if (params.has('taskId')) {
        this.taskId = params.get('taskId');
        plugin.log('✅ 提取到 taskId:', this.taskId);
      } else {
        plugin.warn('⚠️ URL 中未找到 taskId');
      }
      if (params.has('current')) {
        this.current = params.get('current');
        plugin.log('✅ 提取到 current:', this.current);
      } else {
        plugin.warn('⚠️ URL 中未找到 current');
      }
      if (params.has('projectCode')) {
        this.projectCode = params.get('projectCode');
        plugin.log('✅ 提取到 projectCode:', this.projectCode);
      } else {
        plugin.warn('⚠️ URL 中未找到 projectCode');
      }

    },


    async showCustomAlert( title = '提示') {
      const API_BASE_URL = process.env.VUE_APP_API_URL;
      // 获取错误详情
      const errorDetailUrl = `${API_BASE_URL}/dwepErrorMessage/selectZz?businessId=${this.taskId}&projectCode=${this.projectCode}`;
      const detailRes = await fetch(errorDetailUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!detailRes.ok) throw new Error('错误详情接口请求失败');

      const detailData = await detailRes.json();
      plugin.log('错误详情:', detailData);

      if (!detailData.ok || detailData.code !== '0000') {
        Message.warning('获取错误详情失败');
        return false;
      }
      this.dialogVisible = true
      this.errorList = detailData.data;
    },

    findProjectCode() {
      const labels = document.querySelectorAll('.el-form-item__label');
      let transferInLabel = null;

      labels.forEach(label => {
        if (label.textContent.includes('调入项目编码')) {
          transferInLabel = label;
        }
      });

      if (transferInLabel) {
        const input = transferInLabel
            .closest('.el-form-item')
            .querySelector('.el-input__inner');
        this.projectCode = input.value;
        plugin.log('项目编码:', this.projectCode);
      }
    },

    setupSubmitListenerWithObserver() {
      const targetSelector = '.contents'; // 调整为目标容器的正确选择器
      const buttonSelector = 'button.el-button:first-child'; // 提交按钮选择器

      // 先尝试直接查找
      const container = document.querySelector(targetSelector);
      if (container) {
        this.bindSubmitListener(container, buttonSelector);
        return;
      }

      // 未找到，开始监听 DOM 变化
      const observer = new MutationObserver(() => {
        const container = document.querySelector(targetSelector);
        if (container) {
          this.bindSubmitListener(container, buttonSelector);
          observer.disconnect(); // 找到后停止监听
        }
      });

      observer.observe(document.body, {childList: true, subtree: true});

      // 5 秒后超时
      setTimeout(() => {
        observer.disconnect();
        plugin.warn('提交按钮容器未加载，请检查选择器或页面逻辑');
      }, 5000);
    },

    bindSubmitListener(container, buttonSelector) {
      const submitButton = container.querySelector(buttonSelector);

      if (submitButton) {
        // 移除已有监听器（如果已绑定）
        if (submitButton.__hasIntercepted) {
          submitButton.removeEventListener('click', this.handleSubmit);
        }

        // 添加新监听器
        submitButton.addEventListener('click', this.handleSubmit);
        submitButton.__hasIntercepted = true;
        plugin.log('✅ 提交按钮事件重新绑定');
      }
    },

    getSelectedTableRows() {
      const rows = [];
      const table = document.querySelector('.el-table__body-wrapper .el-table__body');
      if (!table) return rows;

      const checkboxes = table.querySelectorAll('.el-checkbox.is-checked');
      checkboxes.forEach(checkbox => {
        const row = checkbox.closest('tr.el-table__row');
        if (row) rows.push(row);
      });

      return rows;
    },

    getTransferInTaskCode(row) {
      const cells = row.querySelectorAll('td');
      if (cells.length > 6) {
        const input = cells[6].querySelector('.el-input__inner');
        return input ? input.title : null;
      }
      return null;
    },

    // 原生提交方法
    originalSubmit() {
      const submitButton = document.querySelector('.contents button.el-button:first-child');
      if (submitButton) {
        // 创建并触发纯原生事件（无信任标志）
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        submitButton.dispatchEvent(clickEvent);
      }
    },

    async handleCustomSubmit(isFromHelpButton = false) {
      const {taskId, projectCode} = this;
      this.extractParamsFromURL(); // 提取 URL 参数
      this.surveyCode = this.getFormFieldValue('设计勘察单号');
      this.projectCode = this.getFormFieldValue('项目编码');


      plugin.log(this.current)
      if(this.current!="dispachSponsor"){
        return true;
      }

      if (!this.taskId || !this.projectCode) {
        Message.warning('缺少必要参数：taskId 或 projectCode');
        return false;
      }

      // 🔍 获取 cookie 中的 user 值
      const cookieString = document.cookie;
      const cookies = cookieString.split(';').reduce((acc, item) => {
        const [key, value] = item.trim().split('=');
        acc[key] = decodeURIComponent(value);
        return acc;
      }, {});

      const user = cookies.user;
      if (!user) {
        Message.warning('无法获取用户信息');
        return false;
      }

      const params = {
        taskId: this.taskId,
        productId: this.projectCode,
        au: user,
        code: this.surveyCode,
        createdBys: this.loginNameValue,
        usrId:this.userID
      };
      const API_BASE_URL = process.env.VUE_APP_API_URL;

      try {
        const response = await fetch(API_BASE_URL+"/dwepErrorMessage/checkMessage", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        });

        if (!response.ok) throw new Error('网络响应异常');

        const data = await response.json();
        plugin.log('✅ 接口返回结果:', data);

        if (data && data.code === '0000') {
          Message.success('文件校验通过');
          return true; // 验证通过返回 true
        } else {

          return false; // 验证失败返回 false
        }
      } catch (error) {
        plugin.error('❌ 请求失败:', error);
        Message.error('请求验证接口出错');
        return false;
      }
    },
    checkAndAddValidationButton() {





      const targetElement = document.querySelector('.operation-content');
      plugin.log('🚀 获取到 .operation-content 模块:', targetElement);

      if (targetElement && !document.getElementById('custom-submit-button')) {
        // 增强的帮助按钮拦截逻辑
        const interceptHelpButton = () => {
          const helpButtons = targetElement.querySelectorAll('.el-button,.ant-btn');
          helpButtons.forEach(button => {
            if (button.textContent.includes('提交') && !button.__hasIntercepted) {
              // 保存原始的 onclick 属性
              const originalOnClick = button.getAttribute('onclick');
              if (originalOnClick) {
                button.removeAttribute('onclick');
                button.__originalOnClick = originalOnClick;
              }

              // 保存原始的事件监听函数
              const originalAddEventListener = button.addEventListener;
              button.addEventListener = function (type, listener, options) {
                // 特殊处理click事件：保存原始监听器引用
                if (type === 'click') {
                  this.__originalClickListeners = this.__originalClickListeners || [];
                  this.__originalClickListeners.push({listener, options});
                }
                // 调用原始addEventListener方法
                return originalAddEventListener.apply(this, arguments);
              };

              // 处理按钮点击事件
              const handleClick = async (e) => {
                // 如果是代码触发的点击，跳过验证
                if (e._isCodeTriggered) return;

                e.preventDefault();
                e.stopImmediatePropagation();

                // 添加初始遮罩层
                const initialMask = document.createElement('div');
                initialMask.style.position = 'fixed';
                initialMask.style.top = '0';
                initialMask.style.left = '0';
                initialMask.style.width = '100%';
                initialMask.style.height = '100%';
                initialMask.style.backgroundColor = 'rgba(0,0,0,0.3)';
                initialMask.style.zIndex = '9998';
                initialMask.style.display = 'flex';
                initialMask.style.justifyContent = 'center';
                initialMask.style.alignItems = 'center';
                initialMask.innerHTML = '<div style="color:white">文件处理中...</div>';
                document.body.appendChild(initialMask);

                try {
                  const isValid = await this.handleCustomSubmit(true);
                  if (isValid) {
                    plugin.log('验证通过，执行原始帮助功能');

                    // 恢复原生的 addEventListener
                    button.addEventListener = originalAddEventListener;

                    // 创建一个标记为代码触发的点击事件
                    const clickEvent = new MouseEvent('click', {
                      bubbles: true,
                      cancelable: true,
                      view: window
                    });
                    clickEvent._isCodeTriggered = true;

                    // 执行内联 onclick
                    if (button.__originalOnClick) {
                      new Function(button.__originalOnClick).call(button);
                    }

                    // 执行保存的事件监听器
                    if (button.__originalClickListeners) {
                      button.__originalClickListeners.forEach(({listener, options}) => {
                        button.addEventListener('click', listener, options);
                        button.dispatchEvent(clickEvent);
                        if (options && options.once) {
                          button.removeEventListener('click', listener, options);
                        }
                      });
                    }

                    // 恢复代理的 addEventListener
                    button.addEventListener = function (type, listener, options) {
                      if (type === 'click') {
                        this.__originalClickListeners = this.__originalClickListeners || [];
                        this.__originalClickListeners.push({listener, options});
                      }
                      return originalAddEventListener.apply(this, arguments);
                    };

                    // 监听第一个弹框出现后移除遮罩
                    const firstDialogObserver = new MutationObserver(() => {
                      const firstDialog = document.querySelector('.el-dialog__wrapper');
                      if (firstDialog) {
                        document.body.removeChild(initialMask);
                        firstDialogObserver.disconnect();
                      }
                    });

                    firstDialogObserver.observe(document.body, {
                      childList: true,
                      subtree: true
                    });

                    // 5秒超时保护
                    setTimeout(() => {
                      firstDialogObserver.disconnect();
                      document.body.removeChild(initialMask);
                    }, 5000);

                  } else {
                    this.showCustomAlert('验证失败 - 错误详情');
                    plugin.log('验证通过，执行原始帮助功能');

                    // 恢复原生的 addEventListener
                    button.addEventListener = originalAddEventListener;

                    // 创建一个标记为代码触发的点击事件
                    const clickEvent = new MouseEvent('click', {
                      bubbles: true,
                      cancelable: true,
                      view: window
                    });
                    clickEvent._isCodeTriggered = true;

                    // 执行内联 onclick
                    if (button.__originalOnClick) {
                      new Function(button.__originalOnClick).call(button);
                    }

                    // 执行保存的事件监听器
                    if (button.__originalClickListeners) {
                      button.__originalClickListeners.forEach(({ listener, options }) => {
                        button.addEventListener('click', listener, options);
                        button.dispatchEvent(clickEvent);
                        if (options && options.once) {
                          button.removeEventListener('click', listener, options);
                        }
                      });
                    }

                    // 恢复代理的 addEventListener
                    button.addEventListener = function(type, listener, options) {
                      if (type === 'click') {
                        this.__originalClickListeners = this.__originalClickListeners || [];
                        this.__originalClickListeners.push({ listener, options });
                      }
                      return originalAddEventListener.apply(this, arguments);
                    };

                    // 移除遮罩
                    document.body.removeChild(initialMask);

                    // 延迟执行确保弹窗已关闭
                    setTimeout(() => {
                      // 查找审批结果弹窗组件
                      const checkDialog = () => {
                        const proxyDialog = document.querySelector('.form-col.spSty');
                        if (proxyDialog) {
                          // 添加遮罩层
                          const mask = document.createElement('div');
                          mask.style.position = 'fixed';
                          mask.style.top = '0';
                          mask.style.left = '0';
                          mask.style.width = '100%';
                          mask.style.height = '100%';
                          mask.style.backgroundColor = 'rgba(0,0,0,0.3)';
                          mask.style.zIndex = '9998';
                          mask.style.display = 'flex';
                          mask.style.justifyContent = 'center';
                          mask.style.alignItems = 'center';
                          mask.innerHTML = '<div style="color:white">加载中...</div>';
                          document.body.appendChild(mask);

                          // 禁用同意选项
                          const agreeRadio = proxyDialog.querySelector('.el-radio-group label:first-child');
                          if (agreeRadio) {
                            agreeRadio.classList.remove('is-checked');
                            agreeRadio.querySelector('.el-radio__input').classList.remove('is-checked');
                            agreeRadio.querySelector('.el-radio__inner').style.display = 'none';
                            agreeRadio.style.pointerEvents = 'none';
                            agreeRadio.style.opacity = '0.5';
                          }

                          // 选中不同意选项
                          const triggerDisagreeSelection = () => {
                            const MAX_WAIT_TIME = 30000; // 最大等待时间10秒
                            const POLL_INTERVAL = 300; // 轮询间隔300ms
                            let startTime = Date.now();
                            let timer = null;

                            // 保存当前参数值
                            const savedParams = {
                              taskId: this.taskId,
                              projectCode: this.projectCode,
                              current: this.current,
                              userID: this.userID
                            };

                            const selectDisagree = () => {
                              try {
                                // 1. 查找不同意选项
                                const radioGroup = document.querySelector('.form-col.spSty .el-radio-group');
                                if (!radioGroup) return false;

                                // 2. 优先使用Vue实例操作
                                if (radioGroup.__vue__) {
                                  const vm = radioGroup.__vue__;
                                  const disagreeOption = vm.$children.find(child =>
                                      child.$attrs.value === '2'
                                  );
                                  if (disagreeOption) {
                                    disagreeOption.currentValue = true;
                                    vm.$emit('change', '2');
                                    setTimeout(() => {
                                      disagreeOption.$el.click();
                                    }, 100);
                                    return true;
                                  }
                                }

                                // 3. DOM操作作为备用方案
                                const disagreeInput = radioGroup.querySelector('input[value="2"]');
                                if (disagreeInput) {
                                  // 模拟完整点击事件序列
                                  ['mousedown', 'mouseup', 'click'].forEach(type => {
                                    disagreeInput.dispatchEvent(new MouseEvent(type, {
                                      bubbles: true,
                                      cancelable: true,
                                      view: window
                                    }));
                                  });

                                  // 确保UI状态更新
                                  setTimeout(() => {
                                    const disagreeLabel = disagreeInput.closest('label.el-radio');
                                    if (disagreeLabel) {
                                      disagreeLabel.classList.add('is-checked');
                                      disagreeLabel.setAttribute('aria-checked', 'true');
                                      const inputEl = disagreeLabel.querySelector('.el-radio__input');
                                      if (inputEl) inputEl.classList.add('is-checked');
                                    }
                                  }, 50);

                                  // 验证是否选中成功
                                  const isSelected = disagreeInput.checked ||
                                      disagreeInput.closest('.el-radio')?.classList.contains('is-checked');

                                  if (isSelected) {
                                    // 恢复保存的参数
                                    this.taskId = savedParams.taskId;
                                    this.projectCode = savedParams.projectCode;
                                    this.current = savedParams.current;
                                    this.userID = savedParams.userID;

                                    if (mask && mask.parentNode === document.body) {
                                      mask.remove();
                                    }
                                    return true;
                                  }
                                }

                                return false;
                              } catch (error) {
                                plugin.error('选中不同意时出错:', error);
                                return false;
                              }
                            };

                            // 立即尝试一次
                            if (selectDisagree()) return true;

                            // 设置轮询检查
                            timer = setInterval(() => {
                              if (selectDisagree() || Date.now() - startTime > MAX_WAIT_TIME) {
                                clearInterval(timer);
                                if (mask && mask.parentNode === document.body) {
                                  mask.remove();
                                }
                              }
                            }, POLL_INTERVAL);
                          };

                          // 使用MutationObserver确保元素完全加载
                          const dialogObserver = new MutationObserver(() => {
                            if (triggerDisagreeSelection()) {
                              dialogObserver.disconnect();
                            }
                          });

                          dialogObserver.observe(proxyDialog, {
                            childList: true,
                            subtree: true,
                            attributes: true
                          });

                          // 10秒超时保护
                          setTimeout(() => {
                            dialogObserver.disconnect();
                            triggerDisagreeSelection();
                          }, 10000);

                          return true;
                        }
                        return false;
                      };

                      // 初始检查
                      if (!checkDialog()) {
                        const observer = new MutationObserver(() => {
                          if (checkDialog()) {
                            observer.disconnect();
                          }
                        });

                        observer.observe(document.body, {
                          childList: true,
                          subtree: true
                        });

                        setTimeout(() => observer.disconnect(), 15000);
                      }
                    }, 100);
                  }
                } catch (error) {
                  plugin.error('验证过程中出错:', error);
                  Message.error('验证失败: ' + error.message);
                  // 确保出错时移除遮罩
                  document.body.removeChild(initialMask);
                }
              };

              // 添加验证点击监听器
              button.addEventListener('click', handleClick, true);
              button.__hasIntercepted = true;
            }
          });
        };

        // 初次尝试拦截
        interceptHelpButton();

        // DOM变化监听
        const observer = new MutationObserver((mutations) => {
          let needsIntercept = false;
          mutations.forEach(mutation => {
            if (mutation.addedNodes.length > 0 ||
                mutation.type === 'attributes' ||
                mutation.type === 'characterData') {
              needsIntercept = true;
            }
          });

          if (needsIntercept) {
            interceptHelpButton();
          }
        });

        observer.observe(targetElement, {
          childList: true,
          subtree: true,
          attributes: true,
          characterData: true
        });
      }
    }


  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }






}


</script>


<style scoped>
.el-loading-mask {
  background-color: rgba(122, 122, 122, .8) !important;
  top: 0 !important;
  left: 0 !important;
  width: 100%;
  height: 100%;
}
.el-loading-text {
  color: #337dff !important;
}

.custom-error-table-dialog .el-dialog__header {
  padding: 20px;
  background-color: #f9fafc;
  border-bottom: 1px solid #e4e7ed;
  font-weight: bold;
}

.custom-error-table-dialog .el-dialog__body {
  padding: 10px 20px 20px 20px;
}
.eldialogIndex{
  z-index: 9999 !important;
}
</style>
