<template>
  <div>
    <component
        v-for="item in componentList"
        :is="item.name"
        :key="item.key"
        v-bind="item.props"
        @close="closeDialog(item.key)"
    />
  </div>
</template>

<script>
import MiitLoginApi from '@/validate/miitLogin/utils/miitLoginApi';
import submitMixin from "@/util/submitMixin";
import { EamMessage, EamLoading } from '@/util/EamElementExt';
import validateSubmit from "@/validate/miitLogin/login/validateSubmit";
import miitLoginDialog from "@/components/eldialog/miitLoginDialog.vue";

export default {
  name: 'MiitLoginApp',
  mixins: [submitMixin],
  components: {
    miitLoginDialog
  },
  data() {
    return {
      // 对话框控制
      dialogVisible: false,
      resultDialogVisible: false,
      
      // 登录表单
      loginForm: {
        username: '',
        password: '',
        smsCode: '',
        toUrl: 'http://txjs.miit.gov.cn'
      },
      
      // 表单验证规则
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少6位', trigger: 'blur' }
        ],
        smsCode: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' },
          { pattern: /^\d{6}$/, message: '验证码应为6位数字', trigger: 'blur' }
        ]
      },
      
      // 状态控制
      isChecking: false,
      isGettingSms: false,
      isLogging: false,
      isProcessing: false,
      
      // 短信验证码相关
      smsCodeEnabled: false,
      smsCountdown: 0,
      smsTimer: null,
      
      // 状态消息
      statusMessage: '',
      statusType: 'info',
      
      // 登录结果
      loginResult: null,
      
      // 历史记录
      loginHistory: [],
      selectedHistory: '',
      
      // API实例
      loginApi: new MiitLoginApi()
    };
  },
  
  computed: {
    canGetSmsCode() {
      return this.loginForm.username && 
             this.loginForm.password && 
             !this.isGettingSms && 
             this.smsCountdown === 0;
    },
    
    canLogin() {
      return this.loginForm.username && 
             this.loginForm.password && 
             this.loginForm.smsCode && 
             !this.isLogging;
    },
    
    smsButtonText() {
      if (this.isGettingSms) {
        return '发送中...';
      } else if (this.smsCountdown > 0) {
        return `${this.smsCountdown}s后重试`;
      } else {
        return '获取验证码';
      }
    }
  },
  
  mounted() {
    this.initSubmitListener([
      {
        key: 'submit',
        validate: (context) => validateSubmit({
          ...context,
          showDialog: this.showDialog
        }),
        buttonText: '自动同步质监申报数据',
        containerSelector: [".operate-btn"]
      }
    ]);
  },
  
  beforeDestroy() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer);
    }
  },
  
  methods: {
    /**
     * 显示登录对话框
     */
    showLoginDialog() {
      this.dialogVisible = true;
      this.resetForm();
      this.setStatus('请输入登录信息', 'info');
    },
    
    /**
     * 处理回车键
     */
    handleEnterKey() {
      if (this.canGetSmsCode && !this.smsCodeEnabled) {
        this.getSmsCode();
      } else if (this.canLogin) {
        this.handleLogin();
      }
    },
    
    /**
     * 获取短信验证码
     */
    async getSmsCode() {
      // 表单验证
      const valid = await this.validateFields(['username', 'password']);
      if (!valid) return;
      
      this.isGettingSms = true;
      this.isProcessing = true;
      this.setStatus('正在发送短信验证码...', 'info');
      
      try {
        const result = await this.loginApi.getSmsCode({
          username: this.loginForm.username,
          password: this.loginForm.password
        });
        
        if (result.success) {
          this.setStatus(result.message, 'success');
          this.smsCodeEnabled = true;
          this.startSmsCountdown();
          
          // 聚焦到验证码输入框
          this.$nextTick(() => {
            this.$refs.loginForm.$el.querySelector('input[placeholder*="验证码"]').focus();
          });
          
          EamMessage.success(result.message);
        } else {
          this.setStatus(`发送失败: ${result.message}`, 'error');
          EamMessage.error(result.message);
        }
      } catch (error) {
        console.error('获取短信验证码失败:', error);
        this.setStatus(`发送失败: ${error.message}`, 'error');
        EamMessage.error('获取短信验证码失败');
      } finally {
        this.isGettingSms = false;
        this.isProcessing = false;
      }
    },
    
    /**
     * 执行登录
     */
    async handleLogin() {
      // 表单验证
      const valid = await this.validateForm();
      if (!valid) return;
      
      this.isLogging = true;
      this.isProcessing = true;
      this.setStatus('正在登录...', 'info');
      
      try {
        const result = await this.loginApi.login({
          username: this.loginForm.username,
          password: this.loginForm.password,
          smsCode: this.loginForm.smsCode,
          toUrl: this.loginForm.toUrl
        });
        
        this.loginResult = result;
        
        if (result.success) {
          this.setStatus('登录成功！', 'success');
          this.saveLoginHistory();
          this.dialogVisible = false;
          this.resultDialogVisible = true;
          EamMessage.success('登录成功');
        } else {
          this.setStatus(`登录失败: ${result.message}`, 'error');
          this.resultDialogVisible = true;
          EamMessage.error(result.message);
        }
      } catch (error) {
        console.error('登录失败:', error);
        this.setStatus(`登录失败: ${error.message}`, 'error');
        EamMessage.error('登录失败');
      } finally {
        this.isLogging = false;
        this.isProcessing = false;
      }
    },
    
    /**
     * 表单验证
     */
    async validateForm() {
      try {
        await this.$refs.loginForm.validate();
        return true;
      } catch (error) {
        return false;
      }
    },
    
    /**
     * 验证指定字段
     */
    async validateFields(fields) {
      try {
        await this.$refs.loginForm.validateField(fields);
        return true;
      } catch (error) {
        return false;
      }
    },
    
    /**
     * 设置状态消息
     */
    setStatus(message, type = 'info') {
      this.statusMessage = message;
      this.statusType = type;
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      this.loginForm.smsCode = '';
      this.smsCodeEnabled = false;
      this.statusMessage = '';
      this.smsCountdown = 0;
      if (this.smsTimer) {
        clearInterval(this.smsTimer);
        this.smsTimer = null;
      }
    },
    
    /**
     * 开始短信倒计时
     */
    startSmsCountdown() {
      this.smsCountdown = 60;
      this.smsTimer = setInterval(() => {
        this.smsCountdown--;
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer);
          this.smsTimer = null;
        }
      }, 1000);
    },
    
    /**
     * 保存登录历史
     */
    saveLoginHistory() {
      const historyItem = {
        username: this.loginForm.username,
        toUrl: this.loginForm.toUrl,
        timestamp: new Date().toISOString()
      };
      
      // 避免重复记录
      const exists = this.loginHistory.some(item => 
        item.username === historyItem.username && item.toUrl === historyItem.toUrl
      );
      
      if (!exists) {
        this.loginHistory.unshift(historyItem);
        // 只保留最近5条记录
        this.loginHistory = this.loginHistory.slice(0, 5);
        localStorage.setItem('miitLoginHistory', JSON.stringify(this.loginHistory));
      }
    },
    
    /**
     * 加载登录历史
     */
    loadLoginHistory() {
      try {
        const history = localStorage.getItem('miitLoginHistory');
        if (history) {
          this.loginHistory = JSON.parse(history);
        }
      } catch (error) {
        console.error('加载登录历史失败:', error);
        this.loginHistory = [];
      }
    },
    
    /**
     * 从历史记录填充表单
     */
    fillFromHistory() {
      if (this.selectedHistory !== '') {
        const historyItem = this.loginHistory[this.selectedHistory];
        if (historyItem) {
          this.loginForm.username = historyItem.username;
          this.loginForm.toUrl = historyItem.toUrl;
        }
      }
    },
    
    // ==================== 结果处理方法 ====================
    
    /**
     * 处理完善企业信息
     */
    handleCompleteInfo() {
      if (this.loginResult.completeInfoUrl) {
        window.open(this.loginResult.completeInfoUrl, '_blank');
      }
      if (this.loginResult.redirectUrl) {
        setTimeout(() => {
          this.handleRedirect();
        }, 2000);
      }
    },
    
    /**
     * 跳过并重定向
     */
    handleSkipAndRedirect() {
      if (this.loginResult.redirectUrl) {
        this.handleRedirect();
      } else {
        this.resultDialogVisible = false;
      }
    },
    
    /**
     * 处理重置密码
     */
    handleResetPassword() {
      if (this.loginResult.resetPasswordUrl) {
        const fullUrl = `http://ucenter.miit.gov.cn${this.loginResult.resetPasswordUrl}`;
        window.open(fullUrl, '_blank');
      }
    },
    
    /**
     * 处理完成注册
     */
    handleFinishRegister() {
      if (this.loginResult.userId) {
        const registerUrl = `http://ucenter.miit.gov.cn/register/getFinshRegister.action?userId=${this.loginResult.userId}`;
        window.open(registerUrl, '_blank');
      }
    },
    
    /**
     * 处理同意协议
     */
    handleAgreeProtocol() {
      if (this.loginResult.userId) {
        const protocolUrl = `http://ucenter.miit.gov.cn/register/protocol_dialog.jsp?userId=${this.loginResult.userId}`;
        window.open(protocolUrl, '_blank');
      }
    },
    
    /**
     * 处理重定向
     */
    handleRedirect() {
      if (this.loginResult.redirectUrl) {
        EamMessage.success('正在跳转...');
        setTimeout(() => {
          window.location.href = this.loginResult.redirectUrl;
        }, 1000);
      }
      this.resultDialogVisible = false;
    },
    
    /**
     * 清除登录历史
     */
    clearLoginHistory() {
      this.$confirm('确定要清除所有登录历史记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loginHistory = [];
        localStorage.removeItem('miitLoginHistory');
        EamMessage.success('登录历史已清除');
      });
    }
  }
};
</script>

