<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
    <!-- 物资支出数量弹窗 -->
    <!-- 修改后的弹窗 -->
    <el-dialog title="" :visible.sync="dialogVisible" width="80%">
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="物资支出低于设计数量预警" name="below"  v-if="belowDesignData.length > 0">
          <div style="margin: 10px 0;">
            <span>批量填写：</span>
            <el-input v-model="batchAbnormalReason" size="small" style="width: 150px; margin-right: 10px;" placeholder="异常原因" />
            <el-select v-model="batchNeedMoreMaterial" size="small" style="width: 120px; margin-right: 10px;" placeholder="是否还需领料" v-if="activeTab === 'below'">
              <el-option label="是" value="1" />
              <el-option label="否" value="2" />
            </el-select>
            <el-button type="primary" @click="batchFill">批量填充</el-button>
            <el-button type="primary" @click="submitWithConfirm">发起确定流程</el-button>
          </div>

          <el-table ref="belowTable" height="450" :data="belowDesignData" border style="width: 100%">
            <el-table-column prop="projectCode" label="项目编码"></el-table-column>
            <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="taskCode" label="任务编码"></el-table-column>
            <el-table-column prop="taskName" label="任务名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="materialCode" label="物料编码"></el-table-column>
            <el-table-column prop="materialName" label="物料名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="expenditure" label="支出量"></el-table-column>
            <el-table-column prop="designAmount" label="设计量"></el-table-column>
            <el-table-column prop="difference" label="差异"></el-table-column>
            <el-table-column label="异常原因">
              <template slot-scope="{ row }">
                <el-input v-model="row.differenceReason" size="small" />
              </template>
            </el-table-column>

            <el-table-column label="是否还需领料" v-if="activeTab === 'below'">
              <template slot-scope="{ row }">
                <el-select v-model="row.isFlag" size="small" placeholder="请选择">
                  <el-option label="是" value="1" />
                  <el-option label="否" value="2" />
                </el-select>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="物资支出高于设计数量预警" name="above" v-if="aboveDesignData.length > 0">
          <div style="margin: 10px 0;">
            <span>批量填写：</span>
            <el-input v-model="batchAbnormalReason" size="small" style="width: 150px; margin-right: 10px;" placeholder="异常原因" />
            <el-button type="primary" @click="batchFill">批量填充</el-button>
            <el-button type="primary" @click="submitWithConfirm">发起确定流程</el-button>
          </div>

          <el-table ref="aboveTable" height="450" :data="aboveDesignData" border style="width: 100%">
            <el-table-column prop="projectCode" label="项目编码"></el-table-column>
            <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="taskCode" label="任务编码" ></el-table-column>
            <el-table-column prop="taskName" label="任务名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="materialCode" label="物料编码"></el-table-column>
            <el-table-column prop="materialName" label="物料名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="expenditure" label="支出量"></el-table-column>
            <el-table-column prop="designAmount" label="设计量"></el-table-column>
            <el-table-column prop="difference" label="差异"></el-table-column>
            <el-table-column label="异常原因">
              <template slot-scope="{ row }">
                <el-input v-model="row.differenceReason" size="small" />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>

import {Message} from 'element-ui'
import {EamLoading} from "@/util/EamElementExt";
import {checkPendingWorkOrderExists, queryReport, sendTodo, update} from "@/api/completionReportApp";
import {logValidation} from "@/api/commonApi";

export default {
  name: 'App',
  components: {

  },
  data() {
    return {
      showTip: true,
      loaded: false,
      componentList: [],
      allocationType: "",
      taskcode: "",
      projectCode: "",
      projectNumber: "",
      observer: null,
      allowSubmit: false,
      errorCode: [],
      whiteList: [],
      title: "",
      dialogVisible: false,
      activeTab: 'below', // 默认显示低于设计数量的tab
      allData: [],
      belowDesignData: [], // 低于设计数量的数据
      aboveDesignData: [], // 高于设计数量的数据
      batchAbnormalReason: '',
      batchNeedMoreMaterial: '',
      requestParamList: [],
      belowOver: true,
      aboveOver: true,
      pmPrincipal: '',
    }
  },
  mounted() {
    this.setupSubmitListenerWithObserver();
  },
  created() {

  },
  beforeUnmount() {
    // 组件销毁时，停止观察
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    handleTabChange(tab) {

    },
    // 检查当前登录人是否为工程实施经理（主）
    async checkIsPmPrincipal() {
      await this.findPmPrincipal;
      const userInfo = this.getUserInfo();
      return userInfo && (userInfo.userName === this.pmPrincipal || userInfo.userNameNew === this.pmPrincipal);
    },
    // 发起确定流程
    submitWithConfirm() {
      // 校验表单数据
      const designData = this.activeTab === 'below' ? this.belowDesignData : this.aboveDesignData;

      if (!this.validateFormData(designData)) {
        return;
      }
      const tit = this.activeTab === 'below' ? '支出低于设计数量' : '支出高于设计数据';
      this.$confirm('确定要发送' + tit + '确定单待办吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.sendTask(designData);
      })
    },
    // 发起确定流程
    async sendTask(designData) {
      try {
        EamLoading.service('正在发送待办...');
        const isUpdateSuccess = await this.updateDesignData(designData);
        if (!isUpdateSuccess) {
          return;
        }

        await this.findProjectNumber();
        const userInfo = this.getUserInfo();
        if (!userInfo) {
          Message.error('获取登录账号失败');
          return;
        }

        const param = {
          projectCode: this.projectCode,
          projectNumber: this.projectNumber,
          warnType: this.activeTab === 'below' ? '1' : '2',
          creatorName: userInfo.email
        };

        await sendTodo(param).then(data => {
          console.log('data:', data);
          param.warnType === '1' ? this.belowOver = true : param.warnType === '2' ? this.aboveOver = true : false;
          if (data.code === '0000') {
            Message.success('发起确定流程成功');
            // 判断两种待办是否全部发送
            if (this.belowOver && this.aboveOver) {
              this.dialogVisible = false;
            }
          } else {
            Message.error(data.msg);
          }
        })
      } catch (error) {
        console.error('Error in sendTodo:', error);
        Message.error('处理过程中发生错误');
      } finally{
        EamLoading.close();
      }
    },
    // 验证表单数据
    validateFormData(data) {
      if (this.activeTab === 'below') {
        for (const item of data) {
          if (!item.isFlag) {
            Message.warning('请选择是否需要领料');
            return false;
          }
          if (!item.differenceReason) {
            Message.warning('请填写异常原因');
            return false;
          }
          if (item.isFlag === '1') {
            Message.warning('存在加领物料，请先处理');
            return false;
          }
        }
      } else {
        for (const item of data) {
          if (!item.differenceReason) {
            Message.warning('请填写异常原因');
            return false;
          }
        }
      }
      return true;
    },
    // 获取当前登录用户信息
    getUserInfo() {
      const userInfo = localStorage.getItem('userInfo');
      return userInfo ? JSON.parse(userInfo) : null;
    },
    // 保存异常原因信息
    async updateDesignData(data) {
      try {
        const res = await update(data);
        if (res.code !== '0000') {
          Message.error('保存原因信息失败');
          return false;
        }
        return true;
      } catch (err) {
        console.error('更新数据失败:', err);
        Message.error('保存原因信息失败');
        return false;
      }
    },
    // 批量填写
    batchFill() {
      const currentData = this.activeTab === 'below' ? this.belowDesignData : this.aboveDesignData;

      if (!currentData.length) {
        Message.warning('当前没有数据可以批量填写');
        return;
      }

      const { batchAbnormalReason, batchNeedMoreMaterial } = this;

      if (!batchAbnormalReason && !batchNeedMoreMaterial) {
        Message.warning('请输入或选择要批量填写的内容');
        return;
      }

      currentData.forEach((row) => {
        if (batchAbnormalReason) this.$set(row, 'differenceReason', batchAbnormalReason);
        if (batchNeedMoreMaterial && this.activeTab === 'below') {
          this.$set(row, 'isFlag', batchNeedMoreMaterial);
        }
      });

      Message.success('批量填写完成');
    },
    // 获取完工报告单号
    async findProjectNumber() {
      const selector = '.el-form-item.order-form-item label[for="completionReportNo"] + .el-form-item__content span';
      const projectNumberSpan = document.querySelector(selector);

      if (!projectNumberSpan) {
        console.error('找不到完工报告单号元素，请检查DOM结构或页面是否加载完成');
        return;
      }

      this.projectNumber = projectNumberSpan.textContent.trim();
      console.log('获取到完工报告单号:', this.projectNumber);
    },
    // 获取工程实施经理（主）
    async findPmPrincipal() {
      const selector = '.el-form-item.order-form-item label[for="pmPrincipal"] + .el-form-item__content span';
      const pmPrincipalSpan = document.querySelector(selector);

      if (!pmPrincipalSpan) {
        console.error('找不到工程实施经理（主）元素，请检查DOM结构或页面是否加载完成');
        return;
      }

      this.pmPrincipal = pmPrincipalSpan.textContent.trim();
      console.log('获取到工程实施经理（主）:', this.pmPrincipal);
    },
    // 获取项目编码
    async findProjectCode() {
      // 查找label包含"项目编码"的输入框
      // 获取项目编码的 input 元素
      const projectCodeInput = document.querySelector('.el-form-item.order-form-item label[for="projectCode"] + .el-form-item__content input');
      // 获取值
      const projectCode = projectCodeInput.value;
      console.log('项目编码:', projectCode);
      this.projectCode = projectCode;
    },
    // 绑定点击事件
    setupSubmitListenerWithObserver() {
      const targetSelector = '.operation-content';
      let bound = false;

      // 检查容器是否已存在
      const container = document.querySelector(targetSelector);
      if (container) {
        this.bindSubmitListener(container);
        bound = true;
        return;
      }

      // 创建观察器等待容器加载
      const observer = new MutationObserver(() => {
        if (bound) return; // 已绑定则退出

        const container = document.querySelector(targetSelector);
        if (container) {
          this.bindSubmitListener(container);
          bound = true;
          observer.disconnect();
        }
      });

      // 开始观察DOM变化
      observer.observe(document.body, {childList: true, subtree: true});

      // 5秒后超时
      setTimeout(() => {
        if (!bound) {
          observer.disconnect();
          console.warn('提交按钮容器未加载，请检查选择器或页面逻辑');
        }
      }, 5000);
    },
    // 重新绑定点击事件
    async triggerNativeSubmit() {
      // 使用 span 文本内容精准查找“提交”按钮
      const buttons = document.querySelectorAll('.operation-content button.el-button');

      let submitButton = null;

      buttons.forEach(button => {
        const span = button.querySelector('span');
        if (span && span.textContent.trim() === '提交') {
          submitButton = button;
        }
      });

      if (!submitButton) return;

      // 临时移除插件监听器
      submitButton.removeEventListener('click', this.handleSubmit, {capture: true});

      // 创建新事件并触发
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });

      // 先触发原生事件
      submitButton.dispatchEvent(clickEvent);

      // 延迟重新绑定插件监听
      setTimeout(() => {
        submitButton.addEventListener('click', this.handleSubmit, {capture: true});
        console.log('重新绑定插件事件监听');
      }, 100);
    },
    // 绑定方法
    bindSubmitListener(container) {
      // 更精确地匹配提交按钮（通过文本内容）
      const buttons = container.querySelectorAll('button.el-button');
      let submitButton = null;

      buttons.forEach(button => {
        const span = button.querySelector('span');
        if (span && span.textContent.trim() === '提交') {
          submitButton = button;
        }
      });

      if (!submitButton || submitButton.__hasPluginListener) return;

      // 绑定插件的事件监听（捕获阶段）
      submitButton.addEventListener('click', this.handleSubmit, {capture: true});
      submitButton.__hasPluginListener = true;
      console.log('✅ 插件事件绑定成功');
    },
    // 提交校验
    handleSubmit: async function (event) {
      this.requestParamList = [];
      const columnIndex = this.getColumnIndex();
      // 阻止事件冒泡和默认行为（完全接管控制权）
      event.stopImmediatePropagation();
      event.preventDefault();
      console.log('提交校验开始')

      // 防止重复处理
      if (event.__isHandledByPlugin) return;
      event.__isHandledByPlugin = true;

      this.errorCode = [];
      await this.findProjectCode();
      await this.findProjectNumber();
      if (this.projectCode === null || this.projectCode === '' || this.projectNumber === null || this.projectNumber === '' || !this.showTip) {
        console.log(this.projectCode)
        await this.triggerNativeSubmit();
        return;
      }

      const isPmPrincipal = await this.checkIsPmPrincipal();
      if (!isPmPrincipal) {
        console.log("当前登录人非工程实施经理，跳过校验")
        await this.triggerNativeSubmit();
        return;
      }

      try {
        EamLoading.service();
        // 1. 查询当前是否存在待办工单
        const workOrderExists = await this.checkWorkOrderExists();
        if (workOrderExists.code === '0000' && Array.isArray(workOrderExists.data)) {
          const pendingWorkOrders = workOrderExists.data.filter(item => item.status !== 'completed');

          if (pendingWorkOrders.length > 0) {
            Message.warning('当前存在预警支出数量确定单审批，请等待确定后进行操作');
            return;
          } else {
            await this.triggerNativeSubmit();
            return;
          }
        }
        // 2. 获取校验任务编码
        const selectedRows = this.getSelectedTableRows();
        const validatePromises = selectedRows.map(async row => {
          const taskCode = await this.getTransferInTaskCode(row, columnIndex);
          return await this.pushTaskCode(taskCode);
        });
        // 等待所有任务完成
        await Promise.all(validatePromises);
        // 开始校验
        const results = await this.validateTaskCode();
        if (results) {
          // 校验通过，触发原生提交
          this.allowSubmit = true;
          await this.triggerNativeSubmit();
          this.allowSubmit = false; // 重置状态
        } else {
          // 3. 校验结果处理
          if (this.belowDesignData.length > 0 || this.aboveDesignData.length > 0) {
            this.dialogVisible = true;
            setTimeout(() => {
              Message.warning('存在物资差异数据，请处理后提交');
            }, 300);
          }
        }
      } catch (error) {
        Message.error("校验失败，请稍后重试");
      } finally {
        EamLoading.close();
      }
    },
    // 获取表格任务编号
    getSelectedTableRows() {
      const rows = [];

      // 获取表格容器
      const table = document.querySelector('.main-warpper .el-table__body-wrapper');

      if (!table) {
        console.warn('未找到目标表格');
        return rows;
      }

      // 获取所有行
      const allRows = table.querySelectorAll('tr.el-table__row');

      console.log('表格table:', allRows);

      allRows.forEach(row => {
        rows.push(row);
      });

      console.log('表格任务编号:', rows);
      return rows;
    },
    // 获取调入任务编码table 所在列序号
    getColumnIndex() {
      // 获取第一个表格的表头
      const firstTableHeader = document.querySelector('.main-warpper .el-table__header-wrapper .el-table__header');

      if (!firstTableHeader) {
        return -1;
      }

      const headerCells = firstTableHeader.querySelectorAll('.el-table__cell');
      // 遍历查找包含"任务编码"的单元格
      let targetIndex = -1;

      headerCells.forEach((cell, index) => {
        const cellText = cell.querySelector('.cell')?.textContent?.trim();
        if (cellText && cellText.includes('任务编码')) {
          targetIndex = index;
        }
      });

      // 返回结果（注意：索引从0开始）
      console.log('任务编码所在列的序号:', targetIndex);
      return targetIndex;
    },
    // 获取任务编码
    async getTransferInTaskCode(row, columnIndex) {
      const cells = row.querySelectorAll('td');

      // 检查列索引是否有效
      if (cells.length <= columnIndex) {
        console.warn('无效的列索引:', columnIndex);
        return null;
      }

      const cell = cells[columnIndex];

      // 查找包含任务编码的元素
      const tooltipElement = cell.querySelector('.el-tooltip');

      // 获取文本内容（优先使用 textContent）
      const taskCode = tooltipElement ? tooltipElement.textContent.trim() : null;

      console.log('获取到的任务编码:', taskCode);
      return taskCode;
    },
    // 添加请求参数
    async pushTaskCode(taskCode) {
      const requestParam = {
        projectCode: this.projectCode,
        projectNumber: this.projectNumber,
        taskCode: taskCode,
      }
      this.requestParamList.push(requestParam);
      return true
    },
    // 判断是否存在确定单据
    async checkWorkOrderExists() {
      const requestParam = {
        projectNumber: this.projectNumber,
      }
      const response = await checkPendingWorkOrderExists(requestParam);
      console.log('查询结果:', response);
      return response;
    },
    /**
     * 校验任务编码
     * @param {string} taskCode 需要校验的任务编码
     * @returns {Promise<boolean>} 返回校验结果
     */
    async validateTaskCode() {
      // 重置数据
      this.belowDesignData = [];
      this.aboveDesignData = [];
      let validationResult = false;
      console.log('开始校验任务编码:', this.requestParamList);
      try {
        const response = await queryReport(this.requestParamList);
        console.log('查询结果:', response);
        if (response.code === '0000' && response.data.length > 0) {
          this.belowDesignData = response.data.filter(item => item.differenceType === '1');
          this.aboveDesignData = response.data.filter(item => item.differenceType === '2');
          if (this.belowDesignData.length === 0 && this.aboveDesignData.length === 0 ) {
            validationResult = true;
          }
          // 设置默认激活 tab
          const hasBelow = this.belowDesignData.length > 0;
          const hasAbove = this.aboveDesignData.length > 0;

          this.activeTab = hasBelow ? 'below' : (hasAbove ? 'above' : '');
          this.belowOver = !hasBelow;
          this.aboveOver = !hasAbove;
          this.allData.push(...this.belowDesignData, ...this.aboveDesignData)
        }
        Message.warning(response.msg);
      } catch (error) {
        console.error('校验任务编码失败:', error);
        return false;
      } finally {
        // 无论成功失败都记录日志
        await logValidation({
          businessType: 'COMPLETION_REPORT',
          projectCode: this.projectCode,
          validationResults: validationResult,
          requestParams: JSON.stringify(this.requestParamList),
          responseData: JSON.stringify(this.allData),
          isSuccessful: validationResult,
          errorMessages: validationResult ? '' : `存在物资支出设计数据差异数据：完工报告单号${this.projectNumber};项目编号${this.projectCode} `,
          pageUrl: window.location.href
        });
      }
      return validationResult;
    }
  }
}
</script>
<style scoped>::v-deep .el-dialog__body {
  padding: 10px 20px 40px 20px;
  background-color: #f9f9f9;
  max-height: 600px;
  overflow-y: auto;
}
</style>