<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
    <component
      v-for="item in componentList"
      :is="item.name"
      :key="item.key"
      v-bind="item.props"
      @close="closeDialog(item.key)"
    />
  </div>
</template>

<script>
import submitMixin from "@/util/submitMixin";
import validateSubmit from '@/validate/completionReport/add/validateSubmit'
import MaterialWarningDialog from './eldialog/MaterialWarningDialog.vue'
import MaterialRequiredCheckDialog from './eldialog/MaterialRequiredCheckDialog.vue'

export default {
  mixins: [submitMixin],
  components: {
    MaterialWarningDialog,
    MaterialRequiredCheckDialog
  },
  data() {
    return {
      showTip: true,
      componentList: [],
      // 其他页面数据
    }
  },
  mounted() {
    this.initSubmitListener([
      {
        key: 'submit',
        validate: (context) => validateSubmit({
          ...context,
          showDialog: this.showDialog
        }),
        buttonText: '提交',
        containerSelector: [".operation-content"]
      }
    ]);
  },
  methods: {
    showDialog(dialogName, dialogProps) {
      this.componentList = [{
        name: dialogName,
        key: dialogName,
        props: dialogProps
      }]
    },
    closeDialog(key) {
      this.componentList = this.componentList.filter(item => item.key !== key)
    }
  }
}
</script>
<style scoped>::v-deep .el-dialog__body {
  padding: 10px 20px 40px 20px;
  background-color: #f9f9f9;
  max-height: 600px;
  overflow-y: auto;
}
</style>
