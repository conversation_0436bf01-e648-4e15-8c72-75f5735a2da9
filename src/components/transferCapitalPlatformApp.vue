<template>
  <component
      v-for="item in componentList"
      :is="item.name"
      :key="item.key"
      v-bind="item.props"
      @close="closeDialog(item.key)"
  />
</template>

<script>
import submitMixin from "@/util/submitMixin";
import transferCapitalPlatformDialog from './eldialog/transferCapitalPlatformDialog'
import validateSubmit from "@/validate/transferCapitalPlatform/ transmitEam/validateSubmit";

export default {
  name: "transferCapitalPlatformApp",
  mixins: [submitMixin],
  components: {
    transferCapitalPlatformDialog
  },
  data() {
    return {
      componentList: []
    };
  },
  mounted() {
    this.initSubmitListener([
      {
        key: 'submit',
        validate: (context) => validateSubmit({
          ...context,
          showDialog: this.showDialog
        }),
        buttonText: '传送EAM',
        containerSelector: [".bottom-box"]
      }
    ]);
  },
  methods: {
    showDialog(dialogName, dialogProps) {
      this.componentList = [{
        name: dialogName,
        key: dialogName,
        props: dialogProps
      }];
    },
    closeDialog(key) {
      this.componentList = this.componentList.filter(item => item.key !== key);
    }
  },
};
</script>