<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>

    <el-dialog
        title="批量修改差异原因"
        :visible.sync="batchEditDialogVisible"
        width="500px">
      <el-form>
        <el-form-item label="差异原因">
          <el-input v-model="batchEditReason" placeholder="请输入统一的差异原因" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
    <el-button @click="batchEditDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="applyBatchEdit">确认修改</el-button>
  </span>
    </el-dialog>

    <el-dialog
        :title="tableTitle"
        :visible.sync="dialogVisible"
        width="920px" append-to-body class="eldialogIndex">
      <div style="height: 420px">
        <div style="margin-bottom: 10px; text-align: right">
          <el-button type="primary" size="small" @click="showBatchEditDialog">批量修改</el-button>
        </div>
        <el-table :data="errorList" style="width: 100%" :header-cell-style="{
          'background-color': '#f9fcff','color': '#535861','font-weight': 'bold'}" v-loading="detailLoading" height="400px"
                  max-height="400px">
          <el-table-column prop="materialName" label="物料名称"  width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materialCode" label="物料编码" min-width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="expenditureVolume" label="项目支出量" min-width="100" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="auditVolume" label="结算审定量" min-width="100" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="quantityVariance" label="差异数量" min-width="100" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="quantityReason" label="差异原因" min-width="100" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.quantityReason" placeholder="请输入差异原因" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="check()">确 定</el-button>
      </span>
    </el-dialog>
    <component v-for="item in componentList" :is="item.name" :key="item.key" :user-info="userInfo"></component>
  </div>
</template>

<script>
import {fetchBalance, fetchSettlementFees, query,checkx} from "@/api/settlementValidator";
import {logValidation,queryWhiteList} from "@/api/commonApi";

export default {
  name: 'App',
  components: {

  },
  data() {
    return {
      dialogVisible:false,
      showTip: true,
      userInfo: null,
      loaded: false,
      componentList: [],
      allocationType: "",
      observer: null, // 用于存储 MutationObserver 实例
      allowSubmit: false,
      errorCode: [],
      projectCode: "",
      errorList:[],
      tableTitle:"",
      batchEditDialogVisible: false,
      batchEditReason: '',
      loginName:"",
      flag:"",
      isSuccessful:"失败",
      isUse:"true",
      whiteList: [] // 添加白名单数据

    }
  },
  async mounted() {
    this.setupSubmitListenerWithObserver();
  },
  beforeUnmount() {
    // 组件销毁时，停止观察
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    async loadWhiteList() {
      try {
        if (this.projectCode) {
          this.whiteList = await queryWhiteList('MATERIAL_BALANCE', this.projectCode);
        }
      } catch (error) {
        plugin.error('获取白名单失败:', error);
        this.whiteList = [];
      }
    },
    async check() {
      try {
        // 第一步：显示确认弹框
        await this.$confirm('是否确认提交数据并推送审批流?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 用户点击确定后执行原逻辑
        this.dialogVisible = false;
        let username = localStorage.getItem('userInfo');
        let userInfo = JSON.parse(username);
        this.loginName = userInfo.loginName;

        const requestData = {
          projectCode: this.projectCode,
          projectName: '',
          loginName: this.loginName,
          projectBaseId: null,
          items: this.errorList
        };

        const response = await fetchBalance(requestData);
        if (response.success) {
          this.$message.success('差异原因提交成功');
        } else {
          this.$message.error(response.message || '提交失败');
        }
      } catch (error) {
        // 用户点击取消会进入catch
        if (error !== 'cancel') {
          plugin.error('提交过程中出错:', error);
        }
      }
    },

    showBatchEditDialog() {
      this.batchEditReason = '';
      this.batchEditDialogVisible = true;
    },
    applyBatchEdit() {
      if (this.batchEditReason) {
        // 使用$set或重新赋值整个数组来确保响应性
        this.errorList = this.errorList.map(item => {
          return {
            ...item,
            quantityReason: this.batchEditReason
          };
        });
        this.$message.success(`已批量修改 ${this.errorList.length} 条数据的差异原因`);
        this.batchEditDialogVisible = false;
      } else {
        this.$message.warning('请输入差异原因');
      }
    },

    // 获取项目编码
    async findProjectCode() {
      // 在任何方法中
      const projectCodeElement = document.querySelector('label[for="projectCode"] + .el-form-item__content .order_form_content');
      if (projectCodeElement) {
        this.projectCode = projectCodeElement.textContent.trim();
        plugin.log('项目编码:', this.projectCode);
      }
    },

    // 绑定点击事件
    setupSubmitListenerWithObserver() {
      const targetSelector = '.operation-content-header .contents';

      // 创建观察器回调
      const callback = (mutationsList, observer) => {
        const container = document.querySelector(targetSelector);
        if (container) {
          this.bindSubmitListener(container);
          // 不要断开观察器，因为按钮可能会被重新渲染
        }
      };

      // 创建观察器实例（如果尚未创建）
      if (!this.observer) {
        this.observer = new MutationObserver(callback);
        this.observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: false,
          characterData: false
        });

        // 初始检查
        const initialContainer = document.querySelector(targetSelector);
        if (initialContainer) {
          this.bindSubmitListener(initialContainer);
        }
      }
    },
    // 修改 triggerNativeSubmit 方法
    async triggerNativeSubmit() {
      // 查找提交按钮
      const submitButton = this.findSubmitButton();
      if (!submitButton) return;

      // 临时移除插件监听器
      submitButton.removeEventListener('click', this.handleSubmit, {capture: true});

      // 创建新事件并触发
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });

      // 先触发原生事件
      submitButton.dispatchEvent(clickEvent);

      // 延迟重新绑定插件监听
      setTimeout(() => {
        submitButton.addEventListener('click', this.handleSubmit, {capture: true});
        plugin.log('重新绑定插件事件监听');
      }, 100);
    },
    // 查找提交按钮的公共方法
    findSubmitButton() {
      const links = document.querySelectorAll('.operation-content-header .contents a.el-link');
      for (const link of links) {
        const span = link.querySelector('.el-link--inner');
        if (span && span.textContent.trim() === '提交') {
          return link;
        }
      }
      return null;
    },
    // 绑定方法
    bindSubmitListener() {
      const submitButton = this.findSubmitButton();
      if (!submitButton || submitButton.__hasPluginListener) return;

      // 绑定插件的事件监听（捕获阶段）
      submitButton.addEventListener('click', this.handleSubmit, { capture: true });
      submitButton.__hasPluginListener = true;
      plugin.log('✅ 插件事件绑定成功');
    },
    // 提交校验
    handleSubmit: async function (event) {
      // 阻止事件冒泡和默认行为（完全接管控制权）
      event.stopImmediatePropagation();
      event.preventDefault();
      plugin.log('提交校验开始')

      // 防止重复处理
      if (event.__isHandledByPlugin) return;
      event.__isHandledByPlugin = true;

      await this.findProjectCode();
      if (this.projectCode === null || this.projectCode === '' || !this.showTip) {
        plugin.log(this.projectCode)
        await this.triggerNativeSubmit();
        return;
      }

      let result = false;
      try {
        const res = await this.checks();
        if (res == "1") {
          this.$message.warning("项目存在审批，请审批后尝试");
          return;
        } else if (res == "3") {
          result = await this.balance();
          this.dialogVisible = true;
          return;
        } else if (res == "0") {
          result = true;
        } else {
          // 处理其他可能的返回值
          plugin.error("未知的返回值:", res);
        }
      } catch (error) {
        plugin.error("检查过程中出错:", error);
        this.$message.error("操作失败，请重试");
      }
      let results = null;
      if(this.flag != "1") {  // 当flag不为"1"时才进行校验
        results = await this.validate();
        if (results) {
          this.flag = "1";  // 校验通过后设置flag为"1"
        }
      }
      if(!results){
        return;
      }


      if (results&&result) {
        // 校验通过，触发原生提交
        this.allowSubmit = true;
        await this.triggerNativeSubmit();
        this.allowSubmit = false; // 重置状态
      }
    },

    async validate() {
      plugin.log('开始获取三费数据--------');
      try {
        if (this.projectCode === null || this.projectCode === '') {
          return true;
        }
        const isValid = await fetchSettlementFees(this.projectCode);
        plugin.log('三费数据校验结果', isValid);
        return isValid; // 返回校验结果
      } catch (error) {
        plugin.error('校验过程中出错:', error);
        return false;
      }
    },

    async balance() {
      plugin.log('开始获取物资平衡数据--------');

      // 创建加载蒙版
      const loading = this.$loading({
        lock: true,
        text: '正在获取物资平衡数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      try {
        if (this.projectCode === null || this.projectCode === '') {
          loading.close();
          this.isSuccessful="失败"
          return true;
        }

        const res = await query(this.projectCode);
        const message = res.data.message;
        const dwepTaskBalances = res.data.dwepTaskBalances;

        plugin.log('Message:', message);
        plugin.log('Task Balances:', dwepTaskBalances);

        this.errorList = dwepTaskBalances;
        this.tableTitle = message;

        if (message === "") {
          this.isSuccessful="成功"
          return true;
        } else if (message === "项目不存在!") {
          plugin.error('校验过程中出错:项目不存在!');
          this.$message.error('项目不存在!');
        } else if (message.includes("文件格式有误")) {
          this.dialogVisible = true;
        } else if (message.includes("erp无具体项目数据")) {
          this.dialogVisible = true;
        }
        this.isSuccessful="失败"
        return false;
      } catch (error) {
        plugin.error('校验过程中出错:', error);
        this.isUse("false")
        this.$message.error('获取物资平衡数据失败');
        return false;
      } finally {
        try{
        await logValidation({
          businessType: 'CHECK_COMPLETED',
          projectCode:  this.projectCode,
          taskCodes: null,
          validationResults: this.isSuccessful,
          requestParams: JSON.stringify(this.projectCode),
          responseData: JSON.stringify(this.errorList),
          isSuccessful: this.isUse,
          errorMessages: this.tableTitle,
          pageUrl: window.location.href
        });
      } catch (logError) {
        plugin.error('记录主流程日志失败:', logError);
      }
      loading.close(); // 无论成功或失败都关闭蒙版

      }
    },
    async checks(){
      await this.loadWhiteList();
     return await checkx(this.projectCode).then(res=>{
       //白名单校验

       if (Array.isArray(this.whiteList) && this.whiteList.some(item =>
           item.projectNo && item.projectNo.includes(this.projectCode))) {
         plugin.warn(`[${this.projectCode}] 项目编码在白名单中，跳过校验`);
         return "0"; // 白名单项目直接通过
       }
        if(res && res.data && Object.keys(res.data).length > 0){
      if(res.data.status ==="1"){
        return "1";
      }else{
        return "0";
      }
      }else{
        return "3";
      }
      })
    }
  }

}
</script>

