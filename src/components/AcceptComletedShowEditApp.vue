<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
    <component v-for="item in componentList" :is="item.name" :key="item.key" :user-info="userInfo"></component>
  </div>
</template>

<script>
import {fetchSettlementFees} from "@/api/settlementValidator";

export default {
  name: 'App',
  components: {

  },
  data() {
    return {
      showTip: true,
      userInfo: null,
      loaded: false,
      componentList: [],
      allocationType: "",
      observer: null, // 用于存储 MutationObserver 实例
      allowSubmit: false,
      errorCode: [],
      projectCode: "",
    }
  },
  mounted() {
    this.setupSubmitListenerWithObserver();
  },
  beforeUnmount() {
    // 组件销毁时，停止观察
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    // 获取项目编码
    async findProjectCode() {
      // 在任何方法中
      const projectCodeElement = document.querySelector('label[for="projectCode"] + .el-form-item__content .order_form_content');
      if (projectCodeElement) {
        this.projectCode = projectCodeElement.textContent.trim();
        console.log('项目编码:', this.projectCode);
      }
    },

    // 绑定点击事件
    setupSubmitListenerWithObserver() {
      const targetSelector = '.operation-content-header .contents';

      // 创建观察器回调
      const callback = (mutationsList, observer) => {
        const container = document.querySelector(targetSelector);
        if (container) {
          this.bindSubmitListener(container);
          // 不要断开观察器，因为按钮可能会被重新渲染
        }
      };

      // 创建观察器实例（如果尚未创建）
      if (!this.observer) {
        this.observer = new MutationObserver(callback);
        this.observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: false,
          characterData: false
        });

        // 初始检查
        const initialContainer = document.querySelector(targetSelector);
        if (initialContainer) {
          this.bindSubmitListener(initialContainer);
        }
      }
    },
    // 修改 triggerNativeSubmit 方法
    async triggerNativeSubmit() {
      // 查找提交按钮
      const submitButton = this.findSubmitButton();
      if (!submitButton) return;

      // 临时移除插件监听器
      submitButton.removeEventListener('click', this.handleSubmit, {capture: true});

      // 创建新事件并触发
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });

      // 先触发原生事件
      submitButton.dispatchEvent(clickEvent);

      // 延迟重新绑定插件监听
      setTimeout(() => {
        submitButton.addEventListener('click', this.handleSubmit, {capture: true});
        console.log('重新绑定插件事件监听');
      }, 100);
    },
    // 查找提交按钮的公共方法
    findSubmitButton() {
      const links = document.querySelectorAll('.operation-content-header .contents a.el-link');
      for (const link of links) {
        const span = link.querySelector('.el-link--inner');
        if (span && span.textContent.trim() === '提交') {
          return link;
        }
      }
      return null;
    },
    // 绑定方法
    bindSubmitListener() {
      const submitButton = this.findSubmitButton();
      if (!submitButton || submitButton.__hasPluginListener) return;

      // 绑定插件的事件监听（捕获阶段）
      submitButton.addEventListener('click', this.handleSubmit, { capture: true });
      submitButton.__hasPluginListener = true;
      console.log('✅ 插件事件绑定成功');
    },
    // 提交校验
    handleSubmit: async function (event) {
      // 阻止事件冒泡和默认行为（完全接管控制权）
      event.stopImmediatePropagation();
      event.preventDefault();
      console.log('提交校验开始')

      // 防止重复处理
      if (event.__isHandledByPlugin) return;
      event.__isHandledByPlugin = true;

      await this.findProjectCode();
      if (this.projectCode === null || this.projectCode === '' || !this.showTip) {
        console.log(this.projectCode)
        await this.triggerNativeSubmit();
        return;
      }

      // 3. 校验结果处理
      const results = await this.validate();
      if (results) {
        // 校验通过，触发原生提交
        this.allowSubmit = true;
        await this.triggerNativeSubmit();
        this.allowSubmit = false; // 重置状态
      }
    },

    async validate() {
      console.log('开始获取三费数据--------');
      try {
        if (this.projectCode === null || this.projectCode === '') {
          return true;
        }
        const isValid = await fetchSettlementFees(this.projectCode);
        console.log('三费数据校验结果', isValid);
        return isValid; // 返回校验结果
      } catch (error) {
        console.error('校验过程中出错:', error);
        return false;
      }
    }
  }
}
</script>