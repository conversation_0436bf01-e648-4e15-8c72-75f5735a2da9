<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 90px;top:115px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index:10">
    数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
  </el-tag>
    <el-dialog
        title="验证失败 - 错误详情"
        :visible.sync="dialogVisible"
        width="60%" append-to-body>
      <el-table :data="errorList" style="width: 100%" :header-cell-style="{
    'background-color': '#f9fcff','color': '#535861','font-weight': 'bold'}" v-loading="detailLoading">
        <el-table-column type="index" label="序号"  width="50" align="center"></el-table-column>
        <el-table-column prop="errorFile" label="错误文件名称" min-width="120" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="errorMessage" label="错误信息" min-width="100" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="type" label="类型" min-width="80" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <component v-for="item in componentList" :is="item.name" :key="item.key" :user-info="userInfo"></component>
  </div>
</template>
<script>
import { Message, MessageBox } from 'element-ui';

export default {
  name: 'App',
  components: {
  },
  data() {
    return {
      showTip: true,
      userInfo: null,
      loaded: false,
      componentList: [],
      allocationType: "",
      taskcode: "",
      businessId: "",
      projectCode: "",
      observer: null, // 用于存储 MutationObserver 实例
      allowSubmit: false,
      authToken: '',// 存储认证token
      taskId: null,  // 新增：用于存储 taskId
      loginNameElement: null,
      orderFormContent: null,
      spanValue: null,
      spanValue2: null,
      dialogVisible: false,
      errorList: [],
      detailLoading: false,
      localStorageNumbers: null,
      loadingMask: null,
      userID:"",
      loadedComponents: {
        operationContent: false,
        mdsgContainer: false,
        loginInfo: false
      }
    }
  },
  mounted() {
    this.loadingMask = this.createLoadingMask();


    this.waitForElement('.operation-content', () => {
      this.setupObserver();
      this.loadedComponents.operationContent = true;
      this.checkAllLoaded();
    });

    this.waitForElement('.main-container.mdsg-container', () => {
      this.extractParamsFromURL();
      this.loadedComponents.mdsgContainer = true;
      this.checkAllLoaded();
    });

    const observer = new MutationObserver(() => {
      if (document.querySelector('.login-name') && document.querySelector('.order_form_content')) {
        this.getLoginName();
        this.surveyCode = this.getFormFieldValue('设计批复单号');
        this.projectCode = this.getFormFieldValuex('项目编码');
        this.loadedComponents.loginInfo = true;
        this.checkAllLoaded();
        observer.disconnect();
      }
    });
    observer.observe(document.body, { childList: true, subtree: true });
  },
  beforeUnmount() {
    // 组件销毁时，停止观察
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    createLoadingMask() {
      const mask = document.createElement('div');
      mask.style.position = 'fixed';
      mask.style.top = '0';
      mask.style.left = '0';
      mask.style.width = '100%';
      mask.style.height = '100%';
      mask.style.backgroundColor = 'rgba(0,0,0,0.5)';
      mask.style.zIndex = '9999';
      mask.style.display = 'flex';
      mask.style.justifyContent = 'center';
      mask.style.alignItems = 'center';
      mask.innerHTML = '<div style="color:white;font-size:18px;">加载中，请稍候...</div>';
      document.body.appendChild(mask);
      return mask;
    },
    waitForElement(selector, callback) {
      const element = document.querySelector(selector);
      if (element) {
        callback();
      } else {
        setTimeout(() => {
          this.waitForElement(selector, callback);
        }, 100);
      }},
    setupObserver() {
      this.observer = new MutationObserver(() => {
        if (!this.isMounted) {
          this.checkAndAddValidationButton();
          this.isMounted = true;
          this.observer.disconnect();
        }
      });

      this.observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    },
    checkAllLoaded() {
      if (this.loadedComponents.operationContent &&
          this.loadedComponents.mdsgContainer &&
          this.loadedComponents.loginInfo) {
        if (this.loadingMask) {
          document.body.removeChild(this.loadingMask);
          this.loadingMask = null;
        }
      }
    },
    executeAfterContentLoaded(targetElement) {
      clearTimeout(this.retryTimer);
      plugin.log('✅ 操作内容区域已加载:', targetElement);
      this.checkAndAddValidationButton(targetElement);
    },
    getFormFieldValue(labelText) {
      const labels = document.querySelectorAll('label.el-form-item__label');
      for (const label of labels) {
        if (label.textContent.trim().includes(labelText)) {
          const span = label.closest('.el-form-item').querySelector('.order_form_content span');
          if (span) {
            return span.textContent.trim();
          }
        }
      }
      return null;
    },
    getFormFieldValuex(labelText) {
      const labels = document.querySelectorAll('label.el-form-item__label');
      for (const label of labels) {
        // 精确匹配标签文字（避免模糊匹配）
        if (label.textContent.trim() === labelText) {
          const formItem = label.closest('.el-form-item');
          if (!formItem) continue;

          // 精准选择 .order_form_content 下的第一个 div 元素
          const valueDiv = formItem.querySelector('.order_form_content > div');

          if (valueDiv) {
            const text = valueDiv.textContent.trim();
            const match = text.match(/^[A-Z0-9]+/);

            if (match) {
              const cleanText = match[0];
              if (cleanText && !cleanText.includes('查看')) { // 排除干扰词

                return cleanText;
              }
            }

          }

          // 如果没有找到 div，则尝试找 input 或其他元素
          const inputValue = formItem.querySelector('.order_form_content input');
          if (inputValue) {
            return inputValue.value.trim();
          }

          // 最后兜底：遍历子节点，找第一个非空文本节点
          const container = formItem.querySelector('.order_form_content');
          if (container) {
            const children = Array.from(container.children);
            for (const child of children) {
              const text = child.textContent.trim();
              if (text && !text.includes('查看')) {
                return text;
              }
            }
          }
        }
      }
      return null;
    },

    retryGetElement(selector, callback, retries = 10, delay = 500) {
      const tryFind = () => {
        const el = document.querySelector(selector);
        if (el) {
          callback(el);
        } else if (retries > 0) {
          setTimeout(tryFind, delay);
        } else {
          plugin.warn(`❌ 未找到元素 ${selector}`);
        }
      };
      tryFind();
    },
    ensureElementsLoaded() {
      this.retryGetElement('.login-name', () => {
        this.getLoginName();
      });

      this.retryGetElement('.order_form_content', () => {
        this.getOrderFormContentSpans();
        this.getOrderFormSpans();
      });
    },
    getOrderFormContentSpans() {
      const orderFormContent = document.querySelector('.order_form_content');
      if (orderFormContent) {
        const spans = orderFormContent.querySelectorAll('span');
        if (spans.length > 0) {
          this.spanValue = spans.length > 0 ? spans[0].textContent.trim() : null;
          if (this.spanValue) {
            plugin.log('✅ 第一个 span 的值:', this.spanValue);
          }
          plugin.log('✅ .order_form_content 中的 span 值:', this.spanValues);
        } else {
          plugin.warn('⚠️ .order_form_content 中未找到 span 元素');
        }
      } else {
        plugin.warn('⚠️ 未找到 .order_form_content 元素');
      }
    },

    getOrderFormSpans() {
      const orderFormContent = document.querySelector('.order_form_content');
      if (orderFormContent) {
        const spans = orderFormContent.querySelectorAll('span');
        if (spans.length > 0) {
          this.spanValue2 = spans.length > 0 ? spans[0].textContent.trim() : null;
          if (this.spanValue2) {
            plugin.log('✅ 第一个 span 的值:', this.spanValue2);
          }
          plugin.log('✅ .order_form_content 中的 span 值:', this.spanValue2);
        } else {
          plugin.warn('⚠️ .order_form_content 中未找到 span 元素');
        }
      } else {
        plugin.warn('⚠️ 未找到 .order_form_content 元素');
      }
    },
    getLoginName() {
      const loginNameElement = document.querySelector('.login-name');
      if (loginNameElement) {
        this.loginNameValue = loginNameElement.textContent.trim();
        plugin.log('获取到 .login-name 的值:', this.loginNameValue);
        // 可以将值赋给 data 中的变量，例如：
        // this.userInfo = loginNameValue;
      } else {
        plugin.warn('未找到 .login-name 元素');
      }
    },

    extractParamsFromURL() {

      let username = localStorage.getItem('userInfo');
      let userInfo = JSON.parse(username);
      this.userID = userInfo.userID;

        this.taskId = document.querySelector('.main-container.mdsg-container').__vue__.currentBusinessId
    },
    async handleCustomSubmit(isFromHelpButton = false) {
      const {taskId, projectCode} = this;

      this.extractParamsFromURL();
      this.surveyCode = this.getFormFieldValue('设计批复单号');
      this.projectCode = this.getFormFieldValuex('项目编码');

      plugin.log(this.projectCode)

      if (!this.taskId || !this.projectCode) {

        plugin.log(this.projectCode)
        Message.warning('缺少必要参数：项目编码');
        return false;
      }

      // 🔍 获取 cookie 中的 user 值
      const cookieString = document.cookie;
      const cookies = cookieString.split(';').reduce((acc, item) => {
        const [key, value] = item.trim().split('=');
        acc[key] = decodeURIComponent(value);
        return acc;
      }, {});

      const user = cookies.user;
      if (!user) {
        Message.warning('无法获取用户信息');
        return false;
      }

      const params = {
        taskId: this.taskId,
        productId: this.projectCode,
        au: user,
        code: this.surveyCode,
        createdBys: this.loginNameValue,
        userId:this.userID
      };
      const API_BASE_URL = process.env.VUE_APP_API_URL;

      try {
        const response = await fetch(API_BASE_URL+"/dwepErrorMessage/checkMessagePf", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        });

        if (!response.ok) throw new Error('网络响应异常');

        const data = await response.json();
        plugin.log('✅ 接口返回结果:', data);

        if (data && data.code === '0000') {
          Message.success('文件校验通过');
          return true; // 验证通过返回 true
        } else {
          // 获取错误详情
          const errorDetailUrl = `${API_BASE_URL}/dwepErrorMessage/selectZz?businessId=${this.taskId}&projectCode=${this.projectCode}`;
          const detailRes = await fetch(errorDetailUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          if (!detailRes.ok) throw new Error('错误详情接口请求失败');

          const detailData = await detailRes.json();
          plugin.log('错误详情:', detailData);

          if (!detailData.ok || detailData.code !== '0000') {
            Message.warning('获取错误详情失败');
            return false;
          }

          this.dialogVisible = true
          this.errorList = detailData.data;
          return false; // 验证失败返回 false
        }
      } catch (error) {
        plugin.error('❌ 请求失败:', error);
        Message.error('请求验证接口出错');
        return false;
      }
    },

    checkAndAddValidationButton() {
      const targetElement = document.querySelector('.operation-content');
      plugin.log('🚀 获取到 .operation-content 模块:', targetElement);

      if (targetElement && !document.getElementById('custom-submit-button')) {
        // 增强的帮助按钮拦截逻辑
        const interceptHelpButton = () => {
          const helpButtons = targetElement.querySelectorAll('.el-button,.ant-btn');
          helpButtons.forEach(button => {
            if (button.textContent.includes('提交') && !button.__hasIntercepted) {
              // 保存原始的 onclick 属性
              const originalOnClick = button.getAttribute('onclick');
              if (originalOnClick) {
                button.removeAttribute('onclick');
                button.__originalOnClick = originalOnClick;
              }

              // 保存原始的事件监听函数
              const originalAddEventListener = button.addEventListener;
              button.addEventListener = function(type, listener, options) {
                if (type === 'click') {
                  this.__originalClickListeners = this.__originalClickListeners || [];
                  this.__originalClickListeners.push({ listener, options });
                }
                return originalAddEventListener.apply(this, arguments);
              };

              // 处理按钮点击事件
              const handleClick = async (e) => {
                // 如果是代码触发的点击，跳过验证
                if (e._isCodeTriggered) return;

                e.preventDefault();
                e.stopImmediatePropagation();

                // 添加遮罩层
                const overlay = document.createElement('div');
                overlay.style.position = 'fixed';
                overlay.style.top = '0';
                overlay.style.left = '0';
                overlay.style.width = '100%';
                overlay.style.height = '100%';
                overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
                overlay.style.zIndex = '9998';
                overlay.id = 'validation-overlay';
                document.body.appendChild(overlay);

                // 设置弹框监听器
                const dialogObserver = new MutationObserver((mutations) => {
                  const dialog = document.querySelector('.el-dialog,.ant-modal');
                  if (dialog) {
                    document.getElementById('validation-overlay')?.remove();
                    dialogObserver.disconnect();
                  }
                });

                dialogObserver.observe(document.body, {
                  childList: true,
                  subtree: true,
                  attributes: false,
                  characterData: false
                });

                try {
                  const isValid = await this.handleCustomSubmit(true);
                  if (isValid) {
                    plugin.log('验证通过，执行原始帮助功能');

                    // 恢复原生的 addEventListener
                    button.addEventListener = originalAddEventListener;

                    // 创建一个标记为代码触发的点击事件
                    const clickEvent = new MouseEvent('click', {
                      bubbles: true,
                      cancelable: true,
                      view: window
                    });
                    clickEvent._isCodeTriggered = true;

                    // 执行内联 onclick
                    if (button.__originalOnClick) {
                      new Function(button.__originalOnClick).call(button);
                    }

                    // 执行保存的事件监听器
                    if (button.__originalClickListeners) {
                      button.__originalClickListeners.forEach(({ listener, options }) => {
                        button.addEventListener('click', listener, options);
                        button.dispatchEvent(clickEvent);
                        if (options && options.once) {
                          button.removeEventListener('click', listener, options);
                        }
                      });
                    }

                    // 恢复代理的 addEventListener
                    button.addEventListener = function(type, listener, options) {
                      if (type === 'click') {
                        this.__originalClickListeners = this.__originalClickListeners || [];
                        this.__originalClickListeners.push({ listener, options });
                      }
                      return originalAddEventListener.apply(this, arguments);
                    };
                  }
                } catch (error) {
                  plugin.error('验证过程中出错:', error);
                  Message.error('验证失败: ' + error.message);
                  document.getElementById('validation-overlay')?.remove();
                  dialogObserver.disconnect();
                }
              };

              // 添加验证点击监听器
              button.addEventListener('click', handleClick, true);
              button.__hasIntercepted = true;
            }
          });
        };

        // 初次尝试拦截
        interceptHelpButton();

        // DOM变化监听
        const observer = new MutationObserver((mutations) => {
          let needsIntercept = false;
          mutations.forEach(mutation => {
            if (mutation.addedNodes.length > 0 ||
                mutation.type === 'attributes' ||
                mutation.type === 'characterData') {
              needsIntercept = true;
            }
          });

          if (needsIntercept) {
            interceptHelpButton();
          }
        });

        observer.observe(targetElement, {
          childList: true,
          subtree: true,
          attributes: true,
          characterData: true
        });
      }
    }


  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}
</script>


<style scoped>
.el-loading-mask {
  background-color: rgba(122, 122, 122, .8) !important;
  top: 0 !important;
  left: 0 !important;
  width: 100%;
  height: 100%;
}
.el-loading-text {
  color: #337dff !important;
}

.custom-error-table-dialog .el-dialog__header {
  padding: 20px;
  background-color: #f9fafc;
  border-bottom: 1px solid #e4e7ed;
  font-weight: bold;
}

.custom-error-table-dialog .el-dialog__body {
  padding: 10px 20px 20px 20px;
}
.eldialogIndex{
  z-index: 9999 !important;
  margin-top: 20vh;
}

</style>
