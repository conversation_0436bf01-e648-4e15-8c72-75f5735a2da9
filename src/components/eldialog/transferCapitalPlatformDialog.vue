<template>
  <div class="plugin-container">
    <el-dialog
        title="支出数量不能大于物资安装量明细"
        :visible.sync="dialogVisible"
        width="80%"
        :before-close="handleClose"
        :show-close="false"
    >
      <div slot="title" class="dialog-header">
        <span>支出数量不能大于物资安装量明细</span>
        <el-button
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="exportToExcel"
            style="margin-left: 20px;"
        >
          导出Excel
        </el-button>
      </div>
      <el-table
          :data="paginatedUnmatchedAssetList"
          border
          style="width: 100%"
          :header-cell-style="{backgroundColor: '#f5f7fa', color: '#303133', fontWeight: 'bold'}"
      >
        <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="assetId" label="资产ID" align="center" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskCode" label="任务编码" align="center" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskName" label="任务名称" align="center" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialCode" label="物料编码" align="center" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialName" label="物料名称" align="center" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="installQuantity" label="安装量" align="center" min-width="100" ></el-table-column>
        <el-table-column prop="expQuantity" label="支出数量" align="center" min-width="100" ></el-table-column>
        <el-table-column prop="quantityDifference" label="数量差异" align="center" min-width="100" ></el-table-column>
      </el-table>

      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="unmatchedAssetList.length"
      >
      </el-pagination>

      <div class="dialog-footer">
        <el-button @click="handleClose" type="default" size="medium">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "transferCapitalPlatformDialog",
  data() {
    return {
      unmatchedAssetList: [],
      dialogVisible: false,
      currentPage: 1,
      pageSize: 10
    };
  },
  computed: {
    paginatedUnmatchedAssetList() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.unmatchedAssetList.slice(start, end);
    }
  },
  methods: {
    handleClose(done) {
      // 通知父组件关闭对话框
      this.$emit('close');
      // 如果是 before-close 事件调用，需要执行 done 回调
      if (typeof done === 'function') {
        done();
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; // 重置到第一页
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },

    /**
     * 导出Excel功能
     */
    exportToExcel() {
      if (!this.unmatchedAssetList || this.unmatchedAssetList.length === 0) {
        this.$message.warning('没有数据可以导出');
        return;
      }

      try {
        // 准备导出数据
        const exportData = this.unmatchedAssetList.map((item, index) => ({
          '序号': index + 1,
          '任务编码': item.taskCode || '',
          '任务名称': item.taskName || '',
          '物料编码': item.materialCode || '',
          '物料名称': item.materialName || '',
          '安装量': item.installQuantity,
          '支出数量': item.expQuantity,
          '数量差异': item.quantityDifference
        }));

        // 创建Excel内容
        const headers = ['序号', '任务编码', '任务名称', '物料编码', '物料名称', '安装量', '支出数量', '数量差异'];
        this.exportToExcelWithStyles(exportData, headers, '支出数量不能大于物资安装量明细');

        this.$message.success(`成功导出 ${this.unmatchedAssetList.length} 条数据`);
      } catch (error) {
        plugin.error('导出失败:', error);
        this.$message.error('导出失败，请重试');
      }
    },

    /**
     * 导出带样式的Excel文件
     */
    exportToExcelWithStyles(data, headers, sheetName) {
      // 创建HTML表格
      let html = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              table {
                border-collapse: collapse;
                width: 100%;
                font-family: Arial, sans-serif;
              }
              th, td {
                border: 1px solid #ddd;
                text-align: center;
                vertical-align: middle;
                padding: 8px;
              }
              th {
                background-color: #f5f7fa;
                font-weight: bold;
                font-size: 12px;
              }
              td {
                font-size: 11px;
              }
              .col-index { width: 60px; }
              .col-task-code { width: 150px; }
              .col-task-name { width: 200px; }
              .col-material-code { width: 150px; }
              .col-material-name { width: 200px; }
              .col-install-amount { width: 100px; }
              .col-exp-quantity { width: 100px; }
              .col-quantity-diff { width: 100px; }
            </style>
          </head>
          <body>
            <table>
              <thead>
                <tr>
      `;

      // 添加表头
      const colClasses = ['col-index', 'col-task-code', 'col-task-name', 'col-material-code', 'col-material-name', 'col-install-amount', 'col-exp-quantity', 'col-quantity-diff'];
      headers.forEach((header, index) => {
        html += `<th class="${colClasses[index]}">${header}</th>`;
      });

      html += `
                </tr>
              </thead>
              <tbody>
      `;

      // 添加数据行
      data.forEach(row => {
        html += '<tr>';
        headers.forEach((header, index) => {
          html += `<td class="${colClasses[index]}">${row[header]}</td>`;
        });
        html += '</tr>';
      });

      html += `
              </tbody>
            </table>
          </body>
        </html>
      `;

      // 下载Excel文件
      this.downloadExcelFromHTML(html, `${sheetName}.xls`);
    },

    /**
     * 从HTML下载Excel文件
     */
    downloadExcelFromHTML(html, filename) {
      // 添加BOM以支持中文
      const BOM = '\uFEFF';
      const blob = new Blob([BOM + html], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
      });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      URL.revokeObjectURL(url);
    }
  }
}
</script>

<style scoped>
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .dialog-header span {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  .dialog-footer {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #e4e7ed;
  }
</style>
<style>
  .plugin-container .el-dialog__body {
    padding: 10px 20px;
  }

  .plugin-container .el-pagination {
    margin-top: 10px;
    text-align: right;
  }
</style>
