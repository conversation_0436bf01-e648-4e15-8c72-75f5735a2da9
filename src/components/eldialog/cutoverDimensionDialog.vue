<template>
  <div class="plugin-container">
    <!-- 未绑定资产地点任务弹框 -->
    <el-dialog
        title="未关联资产地点的任务清单"
        :visible.sync="unboundLocationDialogVisible"
        width="70%"
        :show-close="false"
    >
      <div slot="title" class="dialog-header">
        <span>未关联资产地点的任务清单</span>
        <el-button
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="exportUnbindTasks"
            style="margin-left: 20px;"
        >
          导出Excel
        </el-button>
      </div>

      <el-table
          :data="paginatedUnboundLocationTaskList"
          border
          style="width: 100%"
          :header-cell-style="{backgroundColor: '#f5f7fa', color: '#303133', fontWeight: 'bold'}"
      >
        <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="taskCode" label="任务编码" align="center" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskName" label="任务名称" align="center" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskType" label="任务专业类型" align="center" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="关联地点状态" align="center" min-width="120">
          <template slot-scope="scope">
            <el-tag type="danger" size="small">未关联</el-tag>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
          @size-change="handleUnbindTaskSizeChange"
          @current-change="handleUnbindTaskCurrentChange"
          :current-page="unbindTaskCurrentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="unbindTaskPageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="unboundLocationTaskList.length"
          style="margin-top: 15px; text-align: right;"
      >
      </el-pagination>

      <div class="dialog-footer">
        <el-button @click="closeUnbindTaskDialog" type="default" size="medium">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 资源校验失败弹框 -->
    <el-dialog
        title="资源维护数量与支出数量不匹配任务清单"
        :visible.sync="resourceExpMatchDialogVisible"
        width="90%"
        :show-close="false"
    >
      <div slot="title" class="dialog-header">
        <span>资源维护数量与支出数量不匹配任务清单</span>
        <el-button
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="exportResourceValidation"
            style="margin-left: 20px;"
        >
          导出Excel
        </el-button>
      </div>

      <el-table
          :data="paginatedResourceValidationList"
          border
          style="width: 100%"
          :header-cell-style="{backgroundColor: '#f5f7fa', color: '#303133', fontWeight: 'bold'}"
      >
        <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="projectCode" label="工程编码" align="center" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="工程名称" align="center" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskNumber" label="任务编码" align="center" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskName" label="任务名称" align="center" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="locationCode" label="地点编码" align="center" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="locationDesc" label="地点描述" align="center" min-width="250" show-overflow-tooltip></el-table-column>
        <el-table-column prop="categoryCode" label="资产类别" align="center" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="categoryDesc" label="资产类别描述" align="center" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="expQuantity" label="支出数量" align="center" min-width="120"></el-table-column>
        <el-table-column prop="resourceCount" label="资源数量" align="center" min-width="100"></el-table-column>
        <el-table-column prop="failureMessage" label="失败原因" align="center" min-width="150" show-overflow-tooltip></el-table-column>
      </el-table>

      <el-pagination
          @size-change="handleResourceValidationSizeChange"
          @current-change="handleResourceValidationCurrentChange"
          :current-page="resourceValidationCurrentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="resourceValidationPageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="resourceExpMatchFailedList.length"
          style="margin-top: 15px; text-align: right;"
      >
      </el-pagination>

      <div class="dialog-footer">
        <el-button @click="closeResourceValidationDialog" type="default" size="medium">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: "cutoverDimensionDialog",
  props: {
    unboundLocationTaskList: { type: Array, default: () => []},
    resourceExpMatchFailedList: { type: Array, default: () => []},
    unboundLocationDialogVisible: { type: Boolean, default: false },
    resourceExpMatchDialogVisible: { type: Boolean, default: false }
  },
  data() {
    return {
      // 未绑定资产地点任务相关数据
      unbindTaskCurrentPage: 1,
      unbindTaskPageSize: 10,
      // 资源校验失败相关数据
      resourceValidationCurrentPage: 1,
      resourceValidationPageSize: 10,
    };
  },
  computed: {
    // 分页后的未绑定任务列表
    paginatedUnboundLocationTaskList() {
      const start = (this.unbindTaskCurrentPage - 1) * this.unbindTaskPageSize;
      const end = start + this.unbindTaskPageSize;
      return this.unboundLocationTaskList.slice(start, end);
    },
    // 分页后的资源校验失败列表
    paginatedResourceValidationList() {
      const start = (this.resourceValidationCurrentPage - 1) * this.resourceValidationPageSize;
      const end = start + this.resourceValidationPageSize;
      return this.resourceExpMatchFailedList.slice(start, end);
    }
  },
  methods: {
    // 显示未绑定任务弹框
    showUnbindTaskDialog(unbindTasks) {
      this.unboundLocationTaskList = unbindTasks;
      this.unbindTaskCurrentPage = 1;
      this.unboundLocationDialogVisible = true;
    },

    // 关闭未绑定任务弹框
    closeUnbindTaskDialog() {
      this.unboundLocationDialogVisible = false;
      this.unboundLocationTaskList = [];
    },

    // 未绑定任务分页 - 每页大小改变
    handleUnbindTaskSizeChange(val) {
      this.unbindTaskPageSize = val;
      this.unbindTaskCurrentPage = 1;
    },

    // 未绑定任务分页 - 当前页改变
    handleUnbindTaskCurrentChange(val) {
      this.unbindTaskCurrentPage = val;
    },

    // 显示资源校验失败弹框
    showResourceValidationDialog(failedItems) {
      // 为每个失败项目添加序号
      this.resourceExpMatchFailedList = failedItems.map((item, index) => ({
        ...item,
        index: index + 1
      }));
      this.resourceValidationCurrentPage = 1;
      this.resourceExpMatchDialogVisible = true;
    },

    // 关闭资源校验失败弹框
    closeResourceValidationDialog() {
      this.resourceExpMatchDialogVisible = false;
      this.resourceExpMatchFailedList = [];
    },

    // 资源校验失败分页 - 每页大小改变
    handleResourceValidationSizeChange(val) {
      this.resourceValidationPageSize = val;
      this.resourceValidationCurrentPage = 1;
    },

    // 资源校验失败分页 - 当前页改变
    handleResourceValidationCurrentChange(val) {
      this.resourceValidationCurrentPage = val;
    },

    // 导出未绑定任务
    exportUnbindTasks() {
      if (!this.unboundLocationTaskList || this.unboundLocationTaskList.length === 0) {
        this.$message.warning('没有数据可以导出');
        return;
      }

      try {
        // 准备导出数据
        const exportData = this.unboundLocationTaskList.map((item, index) => ({
          '序号': index + 1,
          '任务编码': item.taskCode || '',
          '任务名称': item.taskName || '',
          '任务专业类型': item.taskType || '',
          '关联地点状态': '未关联'
        }));

        // 创建Excel内容
        const headers = ['序号', '任务编码', '任务名称', '任务专业类型', '关联地点状态'];
        this.exportUnbindTasksToExcel(exportData, headers, '未关联资产地点的任务清单');

        this.$message.success(`成功导出 ${this.unboundLocationTaskList.length} 条数据`);
      } catch (error) {
        plugin.error('导出失败:', error);
        this.$message.error('导出失败，请重试');
      }
    },

    // 导出资源校验失败数据
    exportResourceValidation() {
      if (!this.resourceExpMatchFailedList || this.resourceExpMatchFailedList.length === 0) {
        this.$message.warning('没有数据可以导出');
        return;
      }

      try {
        // 准备导出数据
        const exportData = this.resourceExpMatchFailedList.map((item, index) => ({
          '序号': index + 1,
          '工程编码': item.projectCode || '',
          '工程名称': item.projectName || '',
          '任务编码': item.taskNumber || '',
          '任务名称': item.taskName || '',
          '地点编码': item.locationCode || '',
          '地点描述': item.locationDesc || '',
          '类别编码': item.categoryCode || '',
          '类别描述': item.categoryDesc || '',
          '支出数量': item.expQuantity || '',
          '资源数量': item.resourceCount || '',
          '失败原因': item.failureMessage || ''
        }));

        // 创建Excel内容
        const headers = ['序号', '工程编码', '工程名称', '任务编码', '任务名称', '地点编码', '地点描述', '类别编码', '类别描述', '支出数量', '资源数量', '失败原因'];
        this.exportResourceValidationToExcel(exportData, headers, '资源维护数量与支出数量不匹配任务清单');

        this.$message.success(`成功导出 ${this.resourceExpMatchFailedList.length} 条数据`);
      } catch (error) {
        plugin.error('导出失败:', error);
        this.$message.error('导出失败，请重试');
      }
    },

    // 导出未绑定任务到Excel
    exportUnbindTasksToExcel(data, headers, sheetName) {
      // 创建HTML表格
      let html = `
                <html>
                    <head>
                        <meta charset="utf-8">
                        <style>
                            table {
                                border-collapse: collapse;
                                width: 100%;
                                font-family: Arial, sans-serif;
                            }
                            th, td {
                                border: 1px solid #ddd;
                                text-align: center;
                                vertical-align: middle;
                                padding: 8px;
                            }
                            th {
                                background-color: #f5f7fa;
                                font-weight: bold;
                                font-size: 12px;
                            }
                            td {
                                font-size: 11px;
                            }
                            .col-index { width: 60px; }
                            .col-task-code { width: 150px; }
                            .col-task-name { width: 200px; }
                            .col-task-type { width: 120px; }
                            .col-status { width: 100px; }
                            .col-asset-status { width: 120px; }
                        </style>
                    </head>
                    <body>
                        <table>
                            <thead>
                                <tr>
            `;

      // 添加表头
      const colClasses = ['col-index', 'col-task-code', 'col-task-name', 'col-task-type', 'col-status', 'col-asset-status'];
      headers.forEach((header, index) => {
        html += `<th class="${colClasses[index]}">${header}</th>`;
      });

      html += `
                                </tr>
                            </thead>
                            <tbody>
            `;

      // 添加数据行
      data.forEach(row => {
        html += '<tr>';
        headers.forEach((header, index) => {
          html += `<td class="${colClasses[index]}">${row[header] || ''}</td>`;
        });
        html += '</tr>';
      });

      html += `
                            </tbody>
                        </table>
                    </body>
                </html>
            `;

      // 下载Excel文件
      this.downloadExcelFromHTML(html, `${sheetName}.xls`);
    },

    // 从HTML下载Excel文件
    downloadExcelFromHTML(html, filename) {
      // 添加BOM以支持中文
      const BOM = '\uFEFF';
      const blob = new Blob([BOM + html], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
      });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      URL.revokeObjectURL(url);
    },

    // 导出资源校验失败数据到Excel
    exportResourceValidationToExcel(data, headers, sheetName) {
      // 创建HTML表格
      let html = `
                <html>
                    <head>
                        <meta charset="utf-8">
                        <style>
                            table {
                                border-collapse: collapse;
                                width: 100%;
                                font-family: Arial, sans-serif;
                            }
                            th, td {
                                border: 1px solid #ddd;
                                text-align: center;
                                vertical-align: middle;
                                padding: 8px;
                            }
                            th {
                                background-color: #f5f7fa;
                                font-weight: bold;
                                font-size: 12px;
                            }
                            td {
                                font-size: 11px;
                            }
                            .col-index { width: 60px; }
                            .col-project-code { width: 150px; }
                            .col-project-name { width: 200px; }
                            .col-task-number { width: 150px; }
                            .col-task-name { width: 200px; }
                            .col-location-code { width: 150px; }
                            .col-location-desc { width: 250px; }
                            .col-category-code { width: 150px; }
                            .col-category-desc { width: 200px; }
                            .col-exp-quantity { width: 100px; }
                            .col-resource-count { width: 100px; }
                            .col-failure-message { width: 150px; }
                        </style>
                    </head>
                    <body>
                        <table>
                            <thead>
                                <tr>
            `;

      // 添加表头
      const colClasses = ['col-index', 'col-project-code', 'col-project-name', 'col-task-number', 'col-task-name',
        'col-location-code', 'col-location-desc', 'col-category-code', 'col-category-desc',
        'col-exp-quantity', 'col-resource-count', 'col-failure-message'];
      headers.forEach((header, index) => {
        html += `<th class="${colClasses[index]}">${header}</th>`;
      });

      html += `
                                </tr>
                            </thead>
                            <tbody>
            `;

      // 添加数据行
      data.forEach(row => {
        html += '<tr>';
        headers.forEach((header, index) => {
          html += `<td class="${colClasses[index]}">${row[header] || ''}</td>`;
        });
        html += '</tr>';
      });

      html += `
                            </tbody>
                        </table>
                    </body>
                </html>
            `;

      // 下载Excel文件
      this.downloadExcelFromHTML(html, `${sheetName}.xls`);
    },
  }
}
</script>

<style scoped>
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .dialog-header span {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  .dialog-footer {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #e4e7ed;
  }
</style>
