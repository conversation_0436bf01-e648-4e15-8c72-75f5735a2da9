<template>
  <el-dialog
      :z-index="9999999999"
      :visible.sync="visible"
      width="80%"
      @close="$emit('close')"
      append-to-body
      :modal="false"
      custom-class="material-warning-dialog">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="物资支出高于设计数量预警" name="above" v-if="localAboveDesignData.length > 0">
        <el-table :data="localAboveDesignData" border style="width: 100%">
          <el-table-column prop="projectCode" label="项目编码" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="taskCode" label="任务编码" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="materialCode" label="物料编码" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="expenditure" label="支出量" width="120" align="center"></el-table-column>
          <el-table-column prop="designAmount" label="设计量" width="120" align="center"></el-table-column>
          <el-table-column prop="difference" label="差异" width="120" align="center"></el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="物资支出低于设计数量预警" name="below" v-if="localBelowDesignData.length > 0">
        <div style="margin: 10px 0;">
          <span>批量填写：</span>
          <el-input v-model="batchAbnormalReason" size="small" style="width: 150px; margin-right: 10px;" placeholder="异常原因" />
          <el-select v-model="batchNeedMoreMaterial" size="small" style="width: 120px; margin-right: 10px;" placeholder="是否还需领料">
            <el-option label="是" value="1" />
            <el-option label="否" value="2" />
          </el-select>
          <el-button type="primary" @click="batchFill">批量填充</el-button>
          <el-button type="primary" @click="submitWithConfirm">发起确定流程</el-button>
        </div>
        <el-table :data="localBelowDesignData" border style="width: 100%">
          <el-table-column prop="projectCode" label="项目编码" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="taskCode" label="任务编码" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="materialCode" label="物料编码" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="expenditure" label="支出量" width="120" align="center"></el-table-column>
          <el-table-column prop="designAmount" label="设计量" width="120" align="center"></el-table-column>
          <el-table-column prop="difference" label="差异" width="120" align="center"></el-table-column>
          <el-table-column label="异常原因">
            <template slot-scope="{ row }">
              <el-input v-model="row.differenceReason" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="是否还需领料" width="120">
            <template slot-scope="{ row }">
              <el-select v-model="row.isFlag" size="small" placeholder="请选择">
                <el-option label="是" value="1" />
                <el-option label="否" value="2" />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')">关闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { update, sendTodo } from '@/api/completionReportApp'
import {getInputValue, getSpanValue, getUserInfo} from "@/util/selector";
import {EamLoading} from "@/util/EamElementExt";
import {Message} from "element-ui";
export default {
  name: 'MaterialWarningDialog',
  props: {
    belowDesignData: { type: Array, default: () => [] },
    aboveDesignData: { type: Array, default: () => [] },
    visible: { type: Boolean, default: true }
  },
  data() {
    return {
      activeTab: this.aboveDesignData.length > 0 ? 'above' : 'below',
      batchAbnormalReason: '',
      batchNeedMoreMaterial: '',
      localBelowDesignData: JSON.parse(JSON.stringify(this.belowDesignData)),
      localAboveDesignData: JSON.parse(JSON.stringify(this.aboveDesignData))
    }
  },
  watch: {
    belowDesignData: {
      handler(val) {
        this.localBelowDesignData = JSON.parse(JSON.stringify(val))
      },
      immediate: true
    },
    aboveDesignData: {
      handler(val) {
        this.localAboveDesignData = JSON.parse(JSON.stringify(val))
      },
      immediate: true
    }
  },
  methods: {
    batchFill() {
      const currentData = this.activeTab === 'below' ? this.localBelowDesignData : this.localAboveDesignData;
      if (!currentData.length) {
        this.$message.warning('当前没有数据可以批量填写');
        return;
      }
      if (!this.batchAbnormalReason && !this.batchNeedMoreMaterial) {
        this.$message.warning('请输入或选择要批量填写的内容');
        return;
      }
      currentData.forEach(row => {
        if (this.batchAbnormalReason) this.$set(row, 'differenceReason', this.batchAbnormalReason);
        if (this.batchNeedMoreMaterial && this.activeTab === 'below') {
          this.$set(row, 'isFlag', this.batchNeedMoreMaterial);
        }
      });
      this.$message.success('批量填写完成');
    },
    async submitWithConfirm() {
      const designData = this.activeTab === 'below' ? this.localBelowDesignData : this.localAboveDesignData;
      if (!this.validateFormData(designData)) return;
      const tit = this.activeTab === 'below' ? '支出低于设计数量' : '支出高于设计数据';
      this.$confirm('确定要发送' + tit + '确定单待办吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          EamLoading.service("待办发送中...");
          // 1. 保存异常原因信息
          const isUpdateSuccess = await this.updateDesignData(designData);
          if (!isUpdateSuccess) return;
          // 2. 获取项目编号、报告单号
          const projectCode = getInputValue('projectCode');
          const projectNumber = getSpanValue('completionReportNo')
          const projectName = getSpanValue('projectName');
          // 3. 获取当前用户信息
          const userInfo = getUserInfo();
          if (!userInfo) {
            this.$message.error('获取登录账号失败');
            return;
          }
          // 4. 发起确定流程
          const param = {
            projectCode,
            projectNumber,
            projectName,
            warnType: this.activeTab === 'below' ? '1' : '2',
            creatorName: userInfo.email
          };
          const data = await sendTodo(param);
          if (data.code === '0000') {
            this.$message.success('发起确定流程成功');
            this.$emit('close');
          } else {
            this.$message.error(data.msg);
          }
        } catch (err) {
          Message.error('待办发送失败，请稍后重试');
        } finally {
          EamLoading.close();
        }
      });
    },
    async updateDesignData(data) {
      try {
        const res = await update(data);
        if (res.code !== '0000') {
          this.$message.error('保存原因信息失败');
          return false;
        }
        return true;
      } catch (err) {
        this.$message.error('保存原因信息失败');
        return false;
      }
    },
    validateFormData(data) {
      if (this.activeTab === 'below') {
        for (const item of data) {
          if (!item.isFlag) {
            this.$message.warning('请选择是否需要领料');
            return false;
          }
          if (!item.differenceReason) {
            this.$message.warning('请填写异常原因');
            return false;
          }
          if (item.isFlag === '1') {
            this.$message.warning('存在加领物料，请先处理');
            return false;
          }
        }
      }
      return true;
    }
  }
}
</script>
<style scoped>
::v-deep .el-dialog__wrapper {
  z-index: 9999999999 !important;
}

::v-deep .el-dialog {
  z-index: 9999999999 !important;
}
</style>