<template>
  <el-dialog :visible.sync="visible" width="80%" @close="$emit('close')">
    <div slot="title" class="dialog-header">
      <span>缺失的资料文件清单</span>
    </div>
      <el-table   :data="paginatedMissFileData" border style="width: 100%"  :header-cell-style="{backgroundColor: '#f5f7fa', color: '#303133', fontWeight: 'bold'}">
        <el-table-column prop="projectCode" label="项目编码" min-width="120"></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskCode" label="任务编码" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskName" label="任务名称" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialsName" label="资料名称" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialsType" label="资料所属阶段" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="{
              'implementation-material': scope.row.materialsType === '1',
              'supervision-material': scope.row.materialsType === '2'
            }">
              {{ scope.row.materialsType === '1' ? '实施资料' : scope.row.materialsType === '2' ? '监理资料' : '' }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          @size-change="handleMissFileSizeChange"
          @current-change="handleMissFileCurrentChange"
          :current-page="missFileCurrentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="missFilePageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="missFileData.length"
          style="margin-top: 15px; text-align: right;"
      >
      </el-pagination>
<!--    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')">关闭</el-button>
    </span>-->
  </el-dialog>
</template>
<script>
export default {
  name: 'MaterialRequiredCheckDialog',
  props: {
    missFileData: { type: Array, default: () => [] },
    visible: { type: Boolean, default: true }
  },
  data() {
    return {
      missFileCurrentPage: 1,
      missFilePageSize: 10
    }
  },
  computed: {
    paginatedMissFileData() {
      const start = (this.missFileCurrentPage - 1) * this.missFilePageSize;
      const end = start + this.missFilePageSize;
      return this.missFileData.slice(start, end);
    }
  },
  mounted() {
  },
  methods: {
    // 资源校验失败分页 - 每页大小改变
    handleMissFileSizeChange(val) {
      this.missFilePageSize = val;
      this.missFileCurrentPage = 1;
    },

    // 资源校验失败分页 - 当前页改变
    handleMissFileCurrentChange(val) {
      this.missFileCurrentPage = val;
    },

  }
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dialog-header span {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}
.implementation-material {
  color: #409EFF; /* 蓝色 */
  font-weight: bold;
}

.supervision-material {
  color: #67C23A; /* 绿色 */
  font-weight: bold;
}


</style>
