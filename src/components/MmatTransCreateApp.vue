<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
    <component v-for="item in componentList" :is="item.name" :key="item.key" :user-info="userInfo"></component>
  </div>
</template>

<script>
import { getCompleteTheReportList } from '@/api/mmatTransCreateApp'; // 引入重构后的API
import { Message } from 'element-ui'
import {logValidation, queryWhiteList} from "@/api/commonApi";
import {EamLoading} from "@/util/EamElementExt";
export default {
  name: 'App',
  components: {

  },
  data() {
    return {
      showTip: true,
      loaded: false,
      componentList: [],
      allocationType: "",
      taskCode: "",
      projectCode: "",
      observer: null, // 用于存储 MutationObserver 实例
      allowSubmit: false,
      errorCode: [],
      whiteList : [],
    }
  },
  mounted() {
    this.setupSubmitListenerWithObserver();
  },
  created() {
  },
  beforeUnmount() {
    // 组件销毁时，停止观察
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    // 获取白名单
    async queryWhiteList() {
      queryWhiteList('MMAT_TRANS_CREATE_APP', this.projectCode).then(res => {
        console.log('白名单:', this.whiteList);
        this.whiteList = res;
      });
    },
    // 获取项目间调拨
    async checkAllocationType() {
      const selectedLabel = document.querySelector('label.el-radio.is-checked .el-radio__label');
      if (selectedLabel) {
        const text = selectedLabel.textContent.trim();
        if (text === '项目间调拨') {
          this.allocationType = '项目间调拨';
        } else if (text === '项目内调拨') {
          this.allocationType = '项目内调拨';
        } else {
          this.allocationType = '未知类型';
        }
      } else {
        console.warn('未找到调拨类型元素');
        this.allocationType = null;
      }
      console.log('调拨类型:', this.allocationType);
    },
    // 调入项目编码
    async findProjectCode() {
      const labels = document.querySelectorAll('.el-form-item__label');
      let transferInLabel = null;

      labels.forEach(label => {
        if (label.textContent.includes('调入项目编码')) {
          transferInLabel = label;
        }
      });

      if (transferInLabel) {
        const input = transferInLabel
            .closest('.el-form-item')
            .querySelector('.el-input__inner');
        this.projectCode = input.value;
        console.log('项目编码:', this.projectCode);
        await queryWhiteList('MMAT_TRANS_CREATE_APP', this.projectCode).then(res => {
          console.log('白名单:', this.whiteList);
          this.whiteList = res;
        });
      }
    },
    // 绑定点击事件
    setupSubmitListenerWithObserver() {
      const targetSelector = '.contents';
      let bound = false;

      // 检查容器是否已存在
      const container = document.querySelector(targetSelector);
      if (container) {
        this.bindSubmitListener(container);
        bound = true;
        return;
      }

      // 创建观察器等待容器加载
      const observer = new MutationObserver(() => {
        if (bound) return; // 已绑定则退出

        const container = document.querySelector(targetSelector);
        if (container) {
          this.bindSubmitListener(container);
          bound = true;
          observer.disconnect();
        }
      });

      // 开始观察DOM变化
      observer.observe(document.body, { childList: true, subtree: true });

      // 5秒后超时
      setTimeout(() => {
        if (!bound) {
          observer.disconnect();
          console.warn('提交按钮容器未加载，请检查选择器或页面逻辑');
        }
      }, 5000);
    },
    // 修改 triggerNativeSubmit 方法
    async triggerNativeSubmit() {
      // 同样使用文本内容查找提交按钮
      const buttons = document.querySelectorAll('.contents button.el-button');
      let submitButton = null;

      buttons.forEach(button => {
        const span = button.querySelector('.el-link--inner');
        if (span && span.textContent.trim() === '提交') {
          submitButton = button;
        }
      });

      if (!submitButton) return;

      // 临时移除插件监听器
      submitButton.removeEventListener('click', this.handleSubmit, {capture: true});

      // 创建新事件并触发
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });

      // 先触发原生事件
      submitButton.dispatchEvent(clickEvent);

      // 延迟重新绑定插件监听
      setTimeout(() => {
        submitButton.addEventListener('click', this.handleSubmit, {capture: true});
        console.log('重新绑定插件事件监听');
      }, 100);
    },
    // 绑定方法
    bindSubmitListener(container) {
      // 更精确地匹配提交按钮（通过文本内容）
      const buttons = container.querySelectorAll('button.el-button');
      let submitButton = null;

      buttons.forEach(button => {
        const span = button.querySelector('.el-link--inner');
        if (span && span.textContent.trim() === '提交') {
          submitButton = button;
        }
      });

      if (!submitButton || submitButton.__hasPluginListener) return;

      // 绑定插件的事件监听（捕获阶段）
      submitButton.addEventListener('click', this.handleSubmit, { capture: true });
      submitButton.__hasPluginListener = true;
      console.log('✅ 插件事件绑定成功');
    },
    // 提交校验
    handleSubmit: async function (event) {
      const columnIndex = this.getColumnIndex();
      console.log('columnIndex:', columnIndex);
      // 阻止事件冒泡和默认行为（完全接管控制权）
      event.stopImmediatePropagation();
      event.preventDefault();
      console.log('提交校验开始')

      // 防止重复处理
      if (event.__isHandledByPlugin) return;
      event.__isHandledByPlugin = true;

      this.errorCode = [];

      // 1. 检查调拨类型、项目间调拨、获取白名单
      await this.checkAllocationType();
      await this.findProjectCode();

      // 修复条件判断：只有项目间调拨且有项目编码时才校验
      if (this.allocationType !== '项目间调拨' || !this.showTip) {
        // 非项目间调拨或没有项目编码，直接放行
        await this.triggerNativeSubmit();
        return;
      }

      try {
        EamLoading.service();
        // 2. 获取选中行并校验任务编码
        const selectedRows = this.getSelectedTableRows();
        const validatePromises = selectedRows.map(async row => {
          const taskCode = await this.getTransferInTaskCode(row, columnIndex);
          return taskCode ? await this.validateTaskCode(taskCode) : Promise.resolve(true);
        });

        // 3. 校验结果处理
        const results = await Promise.all(validatePromises);
        if (results.every(valid => valid)) {
          // 校验通过，触发原生提交
          this.allowSubmit = true;
          await this.triggerNativeSubmit();
          this.allowSubmit = false; // 重置状态
        } else {
          Message.warning(this.errorCode.join(", ")+ `调入任务编码已完工，请检查任务编码`);
        }
      } finally {
        EamLoading.close();
      }
    },
    // 获取表格任务编号
    getSelectedTableRows() {
      const rows = [];
      const table = document.querySelector('.el-table__body-wrapper .el-table__body');
      if (!table) return rows;

      // 获取所有行
      const allRows = table.querySelectorAll('tr.el-table__row');

      allRows.forEach(row => {
        // 检查当前行是否未被勾选
        rows.push(row);
      });
      return rows;
    },
    // 获取调入任务编号table 所在列序号
    getColumnIndex() {
      // 获取所有表头单元格
      const headerCells = document.querySelectorAll('.el-table__header .el-table__cell');

      // 遍历查找包含"调入任务编码"的单元格
      let targetIndex = -1;
      headerCells.forEach((cell, index) => {
        const cellText = cell.querySelector('.cell')?.textContent?.trim();
        if (cellText && cellText.includes('调入任务编码')) {
          targetIndex = index;
        }
      });

      // 返回结果（注意：索引从0开始）
      console.log('调入任务编码所在列的序号:', targetIndex);
      return targetIndex;
    },
    async getTransferInTaskCode(row, columnIndex) {
      const cells = row.querySelectorAll('td');
      if (cells.length > columnIndex) {
        const input = cells[columnIndex].querySelector('.el-input__inner');
        return input ? input.title : null;
      }
      return null;
    },
    /**
     * 校验任务编码（修改后带日志记录版本）
     * @param {string} taskCode 需要校验的任务编码
     * @returns {Promise<boolean>} 返回校验结果
     */
    async validateTaskCode(taskCode) {
      const DOCUMENT_STATUS_APPROVED = '02';  // 完工状态码
      let validationResult = false;          // 默认校验失败
      let responseData = null;               // 记录接口返回数据
      let requestParams = null;              // 记录请求参数

      try {
        // 判断任务编号是否在白名单中
        if (Array.isArray(this.whiteList) && this.whiteList.length > 0) {
          const isWhitelisted = this.whiteList.some(item => {
            // 确保 item.taskCode 存在且为字符串
            return item.taskCode && item.taskCode.includes(taskCode);
          });
          console.log(`任务编号:${taskCode}`);
          if (isWhitelisted) {
            validationResult = true;
            console.log(`任务编号:${taskCode}在白名单中`);
          }
        }

        // 继续请求三方接口进行判断
        if (!validationResult) {
          // 准备请求参数
          requestParams = {
            projectCode: this.projectCode,
            selectDefault: 'PIU_IT_TZJHGLY',
            sysDataAuthQueryDetailVo: {
              companyCode: [],
              secondOrgCode: [],
              key: 'PIU_IT_TZJHGLY',
              value: ''
            },
            checkedKey: [],
            secondOrgCode: [],
            size: 100,
            page: 1,
            taskcode: taskCode
          };

          // 调用校验接口
          await getCompleteTheReportList(requestParams).then(orderData => {
            console.log('接口返回数据:', orderData);
            responseData = orderData;  // 保存接口返回数据
            // 解析接口返回
            const { data } = orderData || {};
            const { body } = data || {};
            const { completionReportList } = body || {};
            const firstItem = Array.isArray(completionReportList) ? completionReportList[0] : null;
            console.log('firstItem:', firstItem);
            // 判断校验结果（02表示已完工，不允许调拨）
            if (firstItem && firstItem.documentStatus === DOCUMENT_STATUS_APPROVED) {
              this.errorCode.push(taskCode);  // 记录错误编码
              validationResult = false;
            } else {
              validationResult = true;
            }
          })

        }
      } catch (error) {
        // 异常处理
        this.errorCode.push(taskCode);
        validationResult = true;
        console.error(`任务编码 ${taskCode} 验证失败:`, error);
      } finally {
        // 无论成功失败都记录日志
        await logValidation({
          businessType: 'MMAT_TRANS_CREATE_APP',
          taskCodes: taskCode,
          projectCode: this.projectCode,
          validationResults: validationResult,
          requestParams: JSON.stringify(requestParams),
          responseData: JSON.stringify(responseData),
          isSuccessful: validationResult,
          errorMessages: validationResult ? '' : `任务编码 ${taskCode} 已完工`,
          pageUrl: window.location.href
        });
      }

      return validationResult;
    }
  }
}
</script>
