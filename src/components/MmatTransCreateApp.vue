<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
    <component v-for="item in componentList" :is="item.name" :key="item.key" :user-info="userInfo"></component>
  </div>
</template>

<script>
import submitMixin from "@/util/submitMixin";
import {validateSubmit} from '@/validate/mmatTrans/create/validateSubmit'

export default {
  mixins: [submitMixin],
  name: 'App',
  data() {
    return {
      showTip: true,
      loaded: false,
      componentList: [],
      allocationType: "",
      taskCode: "",
      projectCode: "",
      errorCode: [],
      whiteList : [],
    }
  },
  mounted() {
    this.initSubmitListener([
      {
        key: 'submit',
        validate: validateSubmit,
        polling: false,
        buttonText: '提交',
        containerSelector: [".contents"]
      }
    ]);
  }
}
</script>
