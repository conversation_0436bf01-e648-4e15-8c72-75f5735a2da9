<template>
  <div>
    <component
        v-for="item in componentList"
        :is="item.name"
        :key="item.key"
        v-bind="item.props"
        @close="closeDialog(item.key)"
    />
  </div>
</template>

<script>
import submitMixin from "@/util/submitMixin";
import cutoverDimensionDialog from './eldialog/cutoverDimensionDialog'
import validateSubmit from "@/validate/cutoverDimension/submit/validateSubmit";

export default {
    name: "cutoverDimensionApp",
    mixins: [submitMixin],
    components: {
      cutoverDimensionDialog
    },
    data() {
      return {
        componentList: []
      };
    },
    mounted() {
      this.initSubmitListener([
        {
          key: 'submit',
          validate: (context) => validateSubmit({
            ...context,
            showDialog: this.showDialog
          }),
          buttonText: '传送EAM',
          containerSelector: [".bottom-box"]
        }
      ]);
    },
    methods: {
        // 绑定点击事件
        setupSubmitListenerWithObserver() {
            const targetSelector = ".operation-content";
            // 检查容器是否已存在
            const submitButton = document.querySelector(targetSelector);
            if (!submitButton) {
                // 延迟重新绑定插件监听
                setTimeout(() => {
                    this.setupSubmitListenerWithObserver();
                }, 1000);
                return;
            }
            // 绑定事件
            bindPluginEventListener(submitButton, this.handleSubmit);
        },
        getBaseOrderInfo() {
            this.baseOrderInfo = document.querySelector(".main-container").__vue__.$vnode.parent.componentInstance.$data;
        },
        getOrderTaskList() {
          return this.baseOrderInfo.taskListData;
        },
        getAcceptanceDate() {
          return document.querySelector(".main-container .order_form_content .el-date-editor--date .el-input__inner").value;
        },
        async getCompanyList() {
            const companyList = await getCurrentCompanyList().then(res => res.data.body.list);
            return companyList.map(item => item.orgId);
        },
        // 提交校验
        async handleSubmit(event) {

            this.getBaseOrderInfo();
            if(!this.baseOrderInfo.projectCode || !this.getAcceptanceDate() || this.getOrderTaskList().length === 0) {
                executeOriginalLogic(event.target);
                return;
            }
            if(this.baseOrderInfo.showHiddenDangerList) {
              // 校验隐患清单中的评估结论不能为空(null)，必须是0或1
              const emptyEvaluationItems = this.baseOrderInfo.hiddenDangerList.filter(item =>
                item.evaluationConclusion === null || item.evaluationConclusion === undefined
              );
              if (emptyEvaluationItems.length > 0) {+
                executeOriginalLogic(event.target);
                return;
              }
            }
            EamLoading.service();
            let isValidTaskLocCode;
            let isValidTaskMatchResources;
            let errorMessage;
            let isSuccessful = true;
            try {
                const taskLocCodeResult = await this.valiTaskLocCode();
                isValidTaskLocCode = taskLocCodeResult.isValid;
                plugin.log('校验割接上线交维站点未关联资产地点:', isValidTaskLocCode);
                if (!isValidTaskLocCode) {
                    errorMessage = '割接上线交维站点未关联资产地点'
                    this.showUnbindTaskDialog(taskLocCodeResult.unbindTasks);
                    return;
                }
                const taskMatchResourcesResult = await this.valiTaskMatchResources();
                isValidTaskMatchResources = taskMatchResourcesResult.isValid;
                plugin.log('校验割接上线交维站点未维护综资资源:', isValidTaskMatchResources);
                if (!isValidTaskMatchResources) {
                    errorMessage = '割接上线交维站点未维护综资资源';
                    if (taskMatchResourcesResult.failedItems && taskMatchResourcesResult.failedItems.length > 0) {
                        this.showResourceValidationDialog(taskMatchResourcesResult.failedItems);
                    } else {
                        EamLoading.close();
                        await MessageBox.alert(taskMatchResourcesResult.validateMessage, '提示', {
                            confirmButtonText: '确定',
                            type: 'warning'
                        });
                    }
                    return;
                }
                executeOriginalLogic(event.target);
            } catch (error) {
                plugin.error(error);
                isSuccessful = false;
                errorMessage = error.message;
            } finally {
                EamLoading.close();
                let valiResult = isValidTaskLocCode && isValidTaskMatchResources;
                const taskList = this.getOrderTaskList();
                const taskCodeArr = taskList.map(item => item.taskCode);
                await logValidation({
                  businessType: 'CHECK_CUTOVER_SUBMIT',
                  projectCode: this.baseOrderInfo.projectCode,
                  taskCodes: taskCodeArr.join(","),
                  validationResults: valiResult,
                  requestParams: null,
                  responseData: null,
                  isSuccessful: isSuccessful,
                  errorMessages: errorMessage,
                  pageUrl: window.location.href
                })
            }
        },
        // 校验任务是否绑定资产地点
        async valiTaskLocCode() {
            const taskList = this.getOrderTaskList();
            const tasks = taskList.map(item =>
                getTaskInfoById({ id: item.taskId }).then(res => res.data.body)
            );
            const results = await Promise.all(tasks);
            const emptyLocTaskList = results.filter(item => !item.assetRegionCode);

            // 构建未绑定任务的详细信息
            const unbindTasks = emptyLocTaskList.map((task, index) => {
                const originalTask = taskList.find(t => t.taskId === task.id);
                return {
                    index: index + 1,
                    taskId: task.id,
                    taskCode: task.taskCode || '',
                    taskName: task.taskLongName || '',
                    taskType: task.taskTypeName || '',
                    status: originalTask?.status || '未知',
                    assetRegionCode: task.assetRegionCode || null
                };
            });

            return {
                isValid: emptyLocTaskList.length === 0,
                unbindTasks: unbindTasks
            };
        },

        // 校验任务是否绑定资源，（（任务与资产地点，资产地点与资源有关联）
        async valiTaskMatchResources() {
            const taskList = this.getOrderTaskList();
            const taskCodeArr = taskList.map(item => item.taskCode);
            const orgIdList = await this.getCompanyList();
            const orgIdStr = orgIdList.join(",");
            let params = {
                orgId : orgIdStr,
                isMatched: 1,
                projectCode: this.baseOrderInfo.projectCode,
                taskNumberList: taskCodeArr,
                startRow: 1,
                pageSize: 10
            }
            const response = await getResourceByValiResult(params);
            if (response.code === '10000') {
                const failedItems = response.data && response.data.failedItems ? response.data.failedItems : [];
                return {
                    isValid: false,
                    failedItems: failedItems,
                    validateMessage: response.data ? response.data.validateMessage : '资源校验失败'
                };
            }
            return {
                isValid: true,
                failedItems: [],
                validateMessage: ''
            };
        },

      /**
       * 显示对话框
       * @param {string} dialogName - 对话框组件名
       * @param {Object} dialogProps - 对话框属性
       */
      showDialog(dialogName, dialogProps) {
        this.componentList = [{
          name: dialogName,
          key: dialogName,
          props: dialogProps
        }];
      },

      /**
       * 关闭对话框
       * @param {string} key - 对话框key
       */
      closeDialog(key) {
        this.componentList = this.componentList.filter(item => item.key !== key);
      }
    },
};
</script>
