<template>
  <div>
    <component
        v-for="item in componentList"
        :is="item.name"
        :key="item.key"
        v-bind="item.props"
        @close="closeDialog(item.key)"
    />
  </div>
</template>

<script>
import submitMixin from "@/util/submitMixin";
import cutoverDimensionDialog from './eldialog/cutoverDimensionDialog'
import validateSubmit from "@/validate/cutoverDimension/submit/validateSubmit";

export default {
    name: "cutoverDimensionApp",
    mixins: [submitMixin],
    components: {
      cutoverDimensionDialog
    },
    data() {
      return {
        componentList: []
      };
    },
    mounted() {
      this.initSubmitListener([
        {
          key: 'submit',
          validate: (context) => validateSubmit({
            ...context,
            showDialog: this.showDialog
          }),
          buttonText: '提交',
          containerSelector: [".operation-content"]
        }
      ]);
    },
    methods: {
      /**
       * 显示对话框
       * @param {string} dialogName - 对话框组件名
       * @param {Object} dialogProps - 对话框属性
       */
      showDialog(dialogName, dialogProps) {
        this.componentList = [{
          name: dialogName,
          key: dialogName,
          props: dialogProps
        }];
      },

      /**
       * 关闭对话框
       * @param {string} key - 对话框key
       */
      closeDialog(key) {
        this.componentList = this.componentList.filter(item => item.key !== key);
      }
    },
};
</script>
