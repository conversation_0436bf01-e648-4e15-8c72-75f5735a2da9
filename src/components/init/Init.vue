<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
  </div>
</template>

<script>
import submitMixin from "@/util/submitMixin";

export default {
  mixins: [submitMixin],
  data() {
    return {
      showTip: true
    }
  },
  mounted() {
    // 使用默认配置
    this.initSubmitListener({
      validate: this.validateForm // 必传的验证方法
    });
  },
  methods: {
    async validateForm() {
      console.log("✅ 开始插件校验逻辑");
      // 实现验证逻辑
      return false; // 或 false
    }
  }
}
</script>