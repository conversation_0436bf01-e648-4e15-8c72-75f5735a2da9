<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
  </div>
</template>

<script>
import submitMixin from "@/util/submitMixin";

export default {
  mixins: [submitMixin],
  data() {
    return {
      showTip: true
    }
  },
  mounted() {
    // ========================
    // submitMixin 使用说明
    // ========================

    // 基本用法（必须传入 validate 方法）
    this.initSubmitListener({
      validate: this.validateForm
    });

    // ------------------------
    // 自定义参数用法示例：
    // ------------------------

    // 1. 自定义按钮文本
    // this.initSubmitListener({
    //   validate: this.validateForm,
    //   buttonText: '保存' // 指定要查找的按钮文本
    // });

    // 2. 自定义容器选择器（单个）
    // this.initSubmitListener({
    //   validate: this.validateForm,
    //   containerSelector: '.custom-container' // 指定按钮所在容器
    // });

    // 3. 自定义容器选择器（多个）
    // this.initSubmitListener({
    //   validate: this.validateForm,
    //   containerSelector: ['.form-container', '.footer-buttons'] // 尝试多个容器
    // });

    // 4. 自定义查找按钮方法（完全控制查找逻辑）
    // this.initSubmitListener({
    //   validate: this.validateForm,
    //   findButtonMethod() {
    //     // 返回按钮DOM元素
    //     return document.querySelector('#special-submit-btn');
    //   }
    // });

    // 5. 自定义提交处理逻辑
    // this.initSubmitListener({
    //   validate: this.validateForm,
    //   submitHandler: async function(event) {
    //     event.preventDefault();
    //     plugin.log('自定义提交处理');

    //     // 执行验证
    //     const isValid = await this.validateForm();
    //     if (isValid) {
    //       // 触发原生提交
    //       this.triggerNativeSubmit();
    //     }
    //   }
    // });

    // 6. 开启调试模式（查看详细日志）
    // this.initSubmitListener({
    //   validate: this.validateForm,
    //   debug: true // 控制台输出详细日志
    // });

    // 7. 完整配置示例
    // this.initSubmitListener({
    //   validate: this.validateForm,
    //   buttonText: '提交订单',
    //   containerSelector: ['.form-container', '.footer-buttons'],
    //   submitHandler: this.customSubmitHandler,
    //   debug: true
    // });
  },
  methods: {
    // 必须实现的验证方法
    async validateForm() {
      plugin.log("✅ 执行插件验证逻辑");
      // 这里实现实际验证逻辑

      // 返回 true 允许提交，false 阻止提交
      return true;
    },

    // 自定义提交处理示例
    async customSubmitHandler(event) {
      event.preventDefault();
      plugin.log('自定义提交处理');

      // 执行验证
      const isValid = await this.validateForm();
      if (isValid) {
        // 触发原生提交
        await this.triggerNativeSubmit();
      }
    }
  }
}
</script>