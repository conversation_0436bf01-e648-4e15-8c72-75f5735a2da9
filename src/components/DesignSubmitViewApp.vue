<template>
  <div>
    <el-tag v-if="showTip"
            style="position: fixed;right: 90px;top:115px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index:10">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
    <el-dialog
        title="验证失败 - 错误详情"
        :visible.sync="dialogVisible"
        width="920px" append-to-body class="eldialogIndex">
      <div style="height: 430px">
        <el-table :data="errorList" style="width: 100%" :header-cell-style="{
    'background-color': '#f9fcff','color': '#535861','font-weight': 'bold'}" v-loading="detailLoading">
          <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
          <el-table-column prop="errorFile" label="错误文件名称" min-width="120" align="center"
                           show-overflow-tooltip></el-table-column>
          <el-table-column prop="errorMessage" label="错误信息" min-width="100" align="center"
                           show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="类型" min-width="80" align="center"
                           show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>


    <component v-for="item in componentList" :is="item.name" :key="item.key" :user-info="userInfo"></component>
  </div>
</template>
<script>
import {Message, MessageBox} from 'element-ui';
import {checkWz} from "@/api/settlementValidator";
import {logValidation,queryWhiteList} from "@/api/commonApi";

export default {
  name: 'App',
  components: {},
  data() {
    return {
      showTip: true,
      userInfo: null,
      loaded: false,
      componentList: [],
      allocationType: "",
      taskcode: "",
      projectCode: "",
      observer: null, // 用于存储 MutationObserver 实例
      allowSubmit: false,
      authToken: '',// 存储认证token
      taskId: null,  // 新增：用于存储 taskId
      loginNameElement: null,
      orderFormContent: null,
      spanValue: null,
      spanValue2: null,
      // ...其他数据
      dialogVisible: false,
      errorList: [],
      detailLoading: false,
      current: null,
      isMounted: false,
      loadingMask: null,
      userID: "",
      taskCode: "",
      res: null,
      errorMessage: "",
      isSuccessful: "",
      isUse: "",
      whiteList: [], // 添加白名单数据
      loadedComponents: {
        operationContent: false,
        mdsgContainer: false,
        loginInfo: false
      }
    }
  },
  async mounted() {

    this.$nextTick(() => {
      this.extractParamsFromURL(); // 提取 URL 参数
      this.setupObserver();
      const observer = new MutationObserver(() => {
        if (document.querySelector('.login-name') && document.querySelector('.order_form_content')) {
          this.getLoginName();
          this.surveyCode = this.getFormFieldValue('设计编制单号');
          this.projectCode = this.getFormFieldValue('项目编码');

          observer.disconnect(); // 找到后停止监听

        }
      });

      observer.observe(document.body, {childList: true, subtree: true});


    })
  },
  beforeUnmount() {
    // 组件销毁时，停止观察
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    async loadWhiteList() {
      try {
        if (this.projectCode) {
          this.whiteList = await queryWhiteList('DESIGN_SUBMIT_VIEW', this.projectCode);
        }
      } catch (error) {
        plugin.error('获取白名单失败:', error);
        this.whiteList = [];
      }
    },
    setupObserver() {
      this.observer = new MutationObserver(() => {
        if (!this.isMounted) {
          this.checkAndAddValidationButton();
          this.isMounted = true;
          this.observer.disconnect();
        }
      });

      this.observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    },
    getFormFieldValue(labelText) {
      const labels = document.querySelectorAll('label.el-form-item__label');
      for (const label of labels) {
        if (label.textContent.trim().includes(labelText)) {
          const span = label.closest('.el-form-item').querySelector('.order_form_content span');
          if (span) {
            return span.textContent.trim();
          }
        }
      }
      return null;
    },


    getLoginName() {
      const loginNameElement = document.querySelector('.login-name');
      if (loginNameElement) {
        this.loginNameValue = loginNameElement.textContent.trim();
        plugin.log('获取到 .login-name 的值:', this.loginNameValue);
        // 可以将值赋给 data 中的变量，例如：
        // this.userInfo = loginNameValue;
      } else {
        plugin.warn('未找到 .login-name 元素');
      }
    },

    extractParamsFromURL() {
      let username = localStorage.getItem('userInfo');
      let userInfo = JSON.parse(username);
      this.userID = userInfo.userID;
      const href = window.location.href;
      let search = '';

      // 判断是否有 hash，优先取 hash 中的内容
      if (href.includes('#')) {
        const hashPart = href.split('#')[1];
        if (hashPart.includes('?')) {
          search = hashPart.split('?')[1];
        }
      } else {
        // 没有 hash 的话取标准 search
        search = new URL(href).search;
      }

      const params = new URLSearchParams(search);

      if (params.has('taskId')) {
        this.taskId = params.get('taskId');
        plugin.log('✅ 提取到 taskId:', this.taskId);
      } else {
        plugin.warn('⚠️ URL 中未找到 taskId');
      }

      if (params.has('projectCode')) {
        this.projectCode = params.get('projectCode');
        plugin.log('✅ 提取到 projectCode:', this.projectCode);
      } else {
        plugin.warn('⚠️ URL 中未找到 projectCode');
      }

      if (params.has('current')) {
        this.current = params.get('current');
        plugin.log('✅ 提取到 current:', this.current);
      } else {
        plugin.warn('⚠️ URL 中未找到 current');
      }
    },

    async showCustomAlert(title = '提示') {
      const API_BASE_URL = process.env.VUE_APP_API_URL;
      // 获取错误详情
      const errorDetailUrl = `${API_BASE_URL}/dwepErrorMessage/selectZz?businessId=${this.taskId}&projectCode=${this.projectCode}`;
      const detailRes = await fetch(errorDetailUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!detailRes.ok) throw new Error('错误详情接口请求失败');

      const detailData = await detailRes.json();
      plugin.log('错误详情:', detailData);

      if (!detailData.ok || detailData.code !== '0000') {
        Message.warning('获取错误详情失败');
        return false;
      }

      this.errorList = detailData.data;
      this.$nextTick(() => {
        this.dialogVisible = true
      })

    },


    checkAndAddValidationButton() {
      const targetElement = document.querySelector('.operation-content');
      if (!targetElement) {
        plugin.log('未找到.operation-content元素，将在500ms后重试');
        setTimeout(ensureButtonBinding, 500);
        return;
      }
      plugin.log('🚀 获取到 .operation-content 模块:', targetElement);


      if (targetElement && !document.getElementById('custom-submit-button')) {
        // 增强的帮助按钮拦截逻辑
        const interceptHelpButton = () => {
          const helpButtons = targetElement.querySelectorAll('.el-button,.ant-btn');
          helpButtons.forEach(button => {
            const allButtons = targetElement.querySelectorAll('.el-button,.ant-btn');
            allButtons.forEach(button => {
              if (button.textContent.includes('批量')) {
                button.style.display = 'none';
              }
            });

            if (button.textContent.includes('提交') &&
                !button.__hasIntercepted) {
              // 保存原始的 onclick 属性
              const originalOnClick = button.getAttribute('onclick');
              if (originalOnClick) {
                button.removeAttribute('onclick');
                button.__originalOnClick = originalOnClick;
              }

              // 保存原始的事件监听函数
              const originalAddEventListener = button.addEventListener;
              button.addEventListener = function (type, listener, options) {
                if (type === 'click') {
                  this.__originalClickListeners = this.__originalClickListeners || [];
                  this.__originalClickListeners.push({listener, options});
                }
                return originalAddEventListener.apply(this, arguments);
              };

              // 处理按钮点击事件
              const handleClick = async (e) => {
                // 如果是代码触发的点击，跳过验证
                if (e._isCodeTriggered) return;

                e.preventDefault();
                e.stopImmediatePropagation();

                const isFlag = await this.checkMesssge()

                if (!isFlag) {
                  const errorMessage = "未完成数智设计平台预算和物资稽核或者稽核不通过，请驳回起草环节";
                  MessageBox.alert(errorMessage, '提示', {
                    confirmButtonText: '确定',
                    type: 'warning'
                  }).then(() => {
                    this.restoreAndExecuteOriginalClick(button, originalAddEventListener);

                    setTimeout(async () => {
                      await this.handleApprovalDialog();
                    }, 100);
                  });
                  return;
                }

                // 添加初始遮罩层
                const initialMask = document.createElement('div');
                initialMask.style.position = 'fixed';
                initialMask.style.top = '0';
                initialMask.style.left = '0';
                initialMask.style.width = '100%';
                initialMask.style.height = '100%';
                initialMask.style.backgroundColor = 'rgba(0,0,0,0.3)';
                initialMask.style.zIndex = '9998';
                initialMask.style.display = 'flex';
                initialMask.style.justifyContent = 'center';
                initialMask.style.alignItems = 'center';
                initialMask.innerHTML = '<div style="color:white">文件解析中...</div>';
                document.body.appendChild(initialMask);

                try {
                  const isValid = await this.handleCustomSubmit(true);
                  if (isValid) {
                    plugin.log('验证通过，执行原始帮助功能');

                    // 恢复原生的 addEventListener
                    button.addEventListener = originalAddEventListener;

                    // 创建一个标记为代码触发的点击事件
                    const clickEvent = new MouseEvent('click', {
                      bubbles: true,
                      cancelable: true,
                      view: window
                    });
                    clickEvent._isCodeTriggered = true;

                    // 执行内联 onclick
                    if (button.__originalOnClick) {
                      new Function(button.__originalOnClick).call(button);
                    }

                    // 执行保存的事件监听器
                    if (button.__originalClickListeners) {
                      button.__originalClickListeners.forEach(({listener, options}) => {
                        button.addEventListener('click', listener, options);
                        button.dispatchEvent(clickEvent);
                        if (options && options.once) {
                          button.removeEventListener('click', listener, options);
                        }
                      });
                    }

                    // 恢复代理的 addEventListener
                    button.addEventListener = function (type, listener, options) {
                      if (type === 'click') {
                        this.__originalClickListeners = this.__originalClickListeners || [];
                        this.__originalClickListeners.push({listener, options});
                      }
                      return originalAddEventListener.apply(this, arguments);
                    };

                    // 监听第一个弹框出现后移除遮罩
                    const firstDialogObserver = new MutationObserver(() => {
                      const firstDialog = document.querySelector('.el-dialog__wrapper');
                      if (firstDialog) {
                        document.body.removeChild(initialMask);
                        firstDialogObserver.disconnect();
                      }
                    });

                    firstDialogObserver.observe(document.body, {
                      childList: true,
                      subtree: true
                    });

                    // 5秒超时保护
                    setTimeout(() => {
                      firstDialogObserver.disconnect();
                      document.body.removeChild(initialMask);
                    }, 5000);

                  } else {
                    this.showCustomAlert('验证失败 - 错误详情');
                    // 恢复原生的 addEventListener
                    button.addEventListener = originalAddEventListener;
                    // 创建一个标记为代码触发的点击事件
                    const clickEvent = new MouseEvent('click', {
                      bubbles: true,
                      cancelable: true,
                      view: window
                    });
                    clickEvent._isCodeTriggered = true;
                    // 执行内联 onclick
                    if (button.__originalOnClick) {
                      new Function(button.__originalOnClick).call(button);
                    }
                    // 执行保存的事件监听器
                    if (button.__originalClickListeners) {
                      button.__originalClickListeners.forEach(({listener, options}) => {
                        button.addEventListener('click', listener, options);
                        button.dispatchEvent(clickEvent);
                        if (options && options.once) {
                          button.removeEventListener('click', listener, options);
                        }
                      });
                    }
                    // 恢复代理的 addEventListener
                    button.addEventListener = function (type, listener, options) {
                      if (type === 'click') {
                        this.__originalClickListeners = this.__originalClickListeners || [];
                        this.__originalClickListeners.push({listener, options});
                      }
                      return originalAddEventListener.apply(this, arguments);
                    };
                    // 移除遮罩
                    document.body.removeChild(initialMask);
                    // 延迟执行确保弹窗已关闭
                    setTimeout(() => {
                      // 查找审批结果弹窗组件
                      const checkDialog = () => {
                        const proxyDialog = document.querySelector('.form-col.spSty');
                        if (proxyDialog) {
                          // 添加遮罩层
                          const mask = document.createElement('div');
                          mask.style.position = 'fixed';
                          mask.style.top = '0';
                          mask.style.left = '0';
                          mask.style.width = '100%';
                          mask.style.height = '100%';
                          mask.style.backgroundColor = 'rgba(0,0,0,0.3)';
                          mask.style.zIndex = '9998';
                          mask.style.display = 'flex';
                          mask.style.justifyContent = 'center';
                          mask.style.alignItems = 'center';
                          mask.innerHTML = '<div style="color:white">加载中...</div>';
                          document.body.appendChild(mask);

                          // 禁用同意选项
                          const agreeRadio = proxyDialog.querySelector('.el-radio-group label:first-child');
                          if (agreeRadio) {
                            agreeRadio.classList.remove('is-checked');
                            agreeRadio.querySelector('.el-radio__input').classList.remove('is-checked');
                            agreeRadio.querySelector('.el-radio__inner').style.display = 'none';
                            agreeRadio.style.pointerEvents = 'none';
                            agreeRadio.style.opacity = '0.5';
                          }

                          // 选中不同意选项
                          const triggerDisagreeSelection = () => {
                            const MAX_WAIT_TIME = 10000; // 最大等待时间10秒
                            const POLL_INTERVAL = 300; // 轮询间隔300ms
                            let startTime = Date.now();
                            let timer = null;

                            // 保存当前参数值
                            const savedParams = {
                              taskId: this.taskId,
                              projectCode: this.projectCode,
                              current: this.current,
                              userID: this.userID
                            };

                            const selectDisagree = () => {
                              try {
                                // 1. 查找不同意选项
                                const radioGroup = document.querySelector('.form-col.spSty .el-radio-group');
                                if (!radioGroup) return false;

                                // 2. 优先使用Vue实例操作
                                if (radioGroup.__vue__) {
                                  const vm = radioGroup.__vue__;
                                  const disagreeOption = vm.$children.find(child =>
                                      child.$attrs.value === '2'
                                  );
                                  if (disagreeOption) {
                                    disagreeOption.currentValue = true;
                                    vm.$emit('change', '2');
                                    setTimeout(() => {
                                      disagreeOption.$el.click();
                                    }, 100);
                                    return true;
                                  }
                                }

                                // 3. DOM操作作为备用方案
                                const disagreeInput = radioGroup.querySelector('input[value="2"]');
                                if (disagreeInput) {
                                  // 模拟完整点击事件序列
                                  ['mousedown', 'mouseup', 'click'].forEach(type => {
                                    disagreeInput.dispatchEvent(new MouseEvent(type, {
                                      bubbles: true,
                                      cancelable: true,
                                      view: window
                                    }));
                                  });

                                  // 确保UI状态更新
                                  setTimeout(() => {
                                    const disagreeLabel = disagreeInput.closest('label.el-radio');
                                    if (disagreeLabel) {
                                      disagreeLabel.classList.add('is-checked');
                                      disagreeLabel.setAttribute('aria-checked', 'true');
                                      const inputEl = disagreeLabel.querySelector('.el-radio__input');
                                      if (inputEl) inputEl.classList.add('is-checked');
                                    }
                                  }, 50);

                                  // 验证是否选中成功
                                  const isSelected = disagreeInput.checked ||
                                      disagreeInput.closest('.el-radio')?.classList.contains('is-checked');

                                  if (isSelected) {
                                    // 恢复保存的参数
                                    this.taskId = savedParams.taskId;
                                    this.projectCode = savedParams.projectCode;
                                    this.current = savedParams.current;
                                    this.userID = savedParams.userID;

                                    if (mask && mask.parentNode === document.body) {
                                      mask.remove();
                                    }
                                    return true;
                                  }
                                }

                                return false;
                              } catch (error) {
                                plugin.error('选中不同意时出错:', error);
                                return false;
                              }
                            };

                            // 立即尝试一次
                            if (selectDisagree()) return true;

                            // 设置轮询检查
                            timer = setInterval(() => {
                              if (selectDisagree() || Date.now() - startTime > MAX_WAIT_TIME) {
                                clearInterval(timer);
                                if (mask && mask.parentNode === document.body) {
                                  mask.remove();
                                }
                              }
                            }, POLL_INTERVAL);
                          };

                          // 使用MutationObserver确保元素完全加载
                          const dialogObserver = new MutationObserver(() => {
                            if (triggerDisagreeSelection()) {
                              dialogObserver.disconnect();
                            }
                          });

                          dialogObserver.observe(proxyDialog, {
                            childList: true,
                            subtree: true,
                            attributes: true
                          });

                          // 10秒超时保护
                          setTimeout(() => {
                            dialogObserver.disconnect();
                            triggerDisagreeSelection();
                          }, 10000);

                          return true;
                        }
                        return false;
                      };

                      // 初始检查
                      if (!checkDialog()) {
                        const observer = new MutationObserver(() => {
                          if (checkDialog()) {
                            observer.disconnect();
                          }
                        });

                        observer.observe(document.body, {
                          childList: true,
                          subtree: true
                        });

                        setTimeout(() => observer.disconnect(), 15000);
                      }
                    }, 100);
                  }
                } catch (error) {
                  plugin.error('验证过程中出错:', error);
                  Message.error('验证失败: ' + error.message);
                  // 确保出错时移除遮罩
                  document.body.removeChild(initialMask);
                }
              };

              // 添加验证点击监听器
              button.addEventListener('click', handleClick, true);
              button.__hasIntercepted = true;


            }
          });
        };

        // 初次尝试拦截
        interceptHelpButton();

        // DOM变化监听
        const observer = new MutationObserver((mutations) => {
          let needsIntercept = false;
          mutations.forEach(mutation => {
            if (mutation.addedNodes.length > 0 ||
                mutation.type === 'attributes' ||
                mutation.type === 'characterData') {
              needsIntercept = true;
            }
          });

          if (needsIntercept) {
            interceptHelpButton();
          }
        });

        observer.observe(targetElement, {
          childList: true,
          subtree: true,
          attributes: true,
          characterData: true
        });
      }


    },

    async handleCustomSubmit(isFromHelpButton = false) {
      this.surveyCode = this.getFormFieldValue('设计编制单号');
      this.projectCode = this.getFormFieldValue('项目编码');

      if (this.current != "projectManager") {
        return true;
      }
      const {taskId, projectCode} = this;
      if (!this.taskId || !this.projectCode) {
        Message.warning('缺少必要参数：taskId 或 projectCode');
        return false;
      }

      // 🔍 获取 cookie 中的 user 值
      const cookieString = document.cookie;
      const cookies = cookieString.split(';').reduce((acc, item) => {
        const [key, value] = item.trim().split('=');
        acc[key] = decodeURIComponent(value);
        return acc;
      }, {});

      const user = cookies.user;
      if (!user) {
        Message.warning('无法获取用户信息');
        return false;
      }

      const params = {
        taskId: this.taskId,
        productId: this.projectCode,
        au: user,
        code: this.surveyCode,
        createdBys: this.loginNameValue,
        userId: this.userID
      };
      const API_BASE_URL = process.env.VUE_APP_API_URL;

      try {
        const response = await fetch(API_BASE_URL + "/dwepErrorMessage/checkMessageZz", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        });

        if (!response.ok) throw new Error('网络响应异常');

        const data = await response.json();
        plugin.log('✅ 接口返回结果:', data);

        if (data && data.code === '0000') {
          Message.success('文件校验通过');
          return true; // 验证通过返回 true
        } else {
          return false; // 验证失败返回 false
        }
      } catch (error) {
        plugin.error('❌ 请求失败:', error);
        Message.error('请求验证接口出错');
        return false;
      }
    },
    //物资与数据稽核
    async checkMesssge() {

      const taskInfo = {
        taskCode: this.getFormFieldValuex('任务编码'),
        projectCode: this.getFormFieldValue('项目编码')
      };
      this.projectCode=this.getFormFieldValue('项目编码')
      await this.loadWhiteList();
      //添加白名单
      if (Array.isArray(this.whiteList) && this.whiteList.some(item =>
          item.projectNo && item.projectNo.includes(taskInfo.projectCode))) {
        plugin.warn(`[${taskInfo.projectCode}] 项目编码在白名单中，跳过校验`);
        this.isUse = true;
        this.isSuccessful = true;
        return true;
      }
      plugin.log(taskInfo)
      try {
        return await checkWz(taskInfo).then(res => {
          this.res = res;
          plugin.log(res)
          if (0 == res.data.expenseAuditIssues.length && 0 == res.data.materialAuditErrors.length && res.data.code == 0) {
            this.isUse = true;
            this.isSuccessful = true;
            return true;
          } else if (res.data.expenseAuditIssues != null || res.data.materialAuditErrors != null) {
            const errorMessage1 = "数智平台报错:预算 " + res.data.expenseAuditIssues.map(item => item.issueReason) + " 物资稽核 " + res.data.materialAuditErrors.map(item => item.errorMessage);

            // 抛出异常以触发 catch 块
            // throw new Error(errorMessage);
            this.errorMessage = errorMessage1;
            this.isUse = true;
            this.isSuccessful = false;
            return false;
          } else {
            return false;
          }
        })
      } catch (error) {
        plugin.error('❌ 请求失败:', error);
        Message.error('请求验证接口出错');
        this.isSuccessful = false;
        this.isUse = false;

        return false;
      } finally {
        await logValidation({
          businessType: 'CHECK_MATERIALS',
          projectCode: this.projectCode,
          taskCodes: this.taskcode,
          validationResults: this.isSuccessful,
          requestParams: JSON.stringify(taskInfo),
          responseData: JSON.stringify(this.res),
          isSuccessful: this.isUse,
          errorMessages: this.errorMessage,
          pageUrl: window.location.href
        });
      }
    },
    getFormFieldValuex(labelText) {
      const labels = document.querySelectorAll('label.el-form-item__label');
      for (const label of labels) {
        // 精确匹配标签文字（避免模糊匹配）
        if (label.textContent.trim() === labelText) {
          const formItem = label.closest('.el-form-item');
          if (!formItem) continue;

          // 精准选择 .order_form_content 下的第一个 div 元素
          const valueDiv = formItem.querySelector('.order_form_content > div');

          if (valueDiv) {
            const text = valueDiv.textContent.trim();
            plugin.log('✅ .order_form_content 中的 div 值:', text)

            // 去除所有中文字符，只保留数字、字母和其他非中文内容
            const cleanText = text.replace(/[\u4e00-\u9fa5]/g, '').trim();

            if (cleanText) {
              return cleanText;
            }
          }

          // 如果没有找到 div，则尝试找 input 或其他元素
          const inputValue = formItem.querySelector('.order_form_content input');
          if (inputValue) {
            return inputValue.value.trim();
          }

          // 最后兜底：遍历子节点，找第一个非空文本节点
          const container = formItem.querySelector('.order_form_content');
          if (container) {
            const children = Array.from(container.children);
            for (const child of children) {
              const text = child.textContent.trim();
              if (text && !text.includes('查看')) {
                return text;
              }
            }
          }
        }
      }
      return null;
    },
    // 恢复按钮原始点击事件并执行
    restoreAndExecuteOriginalClick(button, originalAddEventListener) {
      // 恢复原生的 addEventListener
      button.addEventListener = originalAddEventListener;

      // 创建一个标记为代码触发的点击事件
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      clickEvent._isCodeTriggered = true;

      // 执行内联 onclick
      if (button.__originalOnClick) {
        new Function(button.__originalOnClick).call(button);
      }

      // 执行保存的事件监听器
      if (button.__originalClickListeners) {
        button.__originalClickListeners.forEach(({listener, options}) => {
          button.addEventListener('click', listener, options);
          button.dispatchEvent(clickEvent);
          if (options && options.once) {
            button.removeEventListener('click', listener, options);
          }
        });
      }

      // 恢复代理的 addEventListener
      button.addEventListener = function (type, listener, options) {
        if (type === 'click') {
          this.__originalClickListeners = this.__originalClickListeners || [];
          this.__originalClickListeners.push({listener, options});
        }
        return originalAddEventListener.apply(this, arguments);
      };
    },

    // 处理审批结果对话框
    async handleApprovalDialog() {
      try {
        // 1. 等待审批对话框出现
        const dialog = await this.waitForDialog('.form-col.spSty', 15000);
        if (!dialog) {
          plugin.warn('审批对话框未找到');
          return false;
        }

        // 2. 添加加载遮罩
        const mask = this.createLoadingMask();

        // 3. 禁用同意选项
        this.disableAgreeOption(dialog);

        // 4. 选中不同意选项
        const success = await this.selectDisagreeOption(dialog, {
          taskId: this.taskId,
          projectCode: this.projectCode,
          current: this.current,
          userID: this.userID
        }, mask);

        // 5. 清理资源
        this.cleanupLoadingMask(mask);

        return success;
      } catch (error) {
        plugin.error('处理审批对话框出错:', error);
        return false;
      }
    },

// 辅助方法
    waitForDialog(selector, timeout = 15000) {
      return new Promise((resolve) => {
        // 先立即检查一次
        const dialog = document.querySelector(selector);
        if (dialog) {
          resolve(dialog);
          return;
        }

        // 设置观察器
        const observer = new MutationObserver(() => {
          const dialog = document.querySelector(selector);
          if (dialog) {
            observer.disconnect();
            resolve(dialog);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        // 超时处理
        setTimeout(() => {
          observer.disconnect();
          resolve(null);
        }, timeout);
      });
    },

    createLoadingMask() {
      const mask = document.createElement('div');
      mask.style.cssText = `    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.3);
    z-index: 9998;
    display: flex;
    justify-content: center;
    align-items: center;
  `;
      mask.innerHTML = '<div style="color:white">加载中...</div>';
      document.body.appendChild(mask);
      return mask;
    },

    cleanupLoadingMask(mask) {
      if (mask && mask.parentNode === document.body) {
        mask.remove();
      }
    },

    disableAgreeOption(dialog) {
      const agreeRadio = dialog.querySelector('.el-radio-group label:first-child');
      if (!agreeRadio) return;

      agreeRadio.classList.remove('is-checked');
      const inputEl = agreeRadio.querySelector('.el-radio__input');
      if (inputEl) {
        inputEl.classList.remove('is-checked');
        inputEl.querySelector('.el-radio__inner').style.display = 'none';
      }
      agreeRadio.style.cssText = 'pointer-events: none; opacity: 0.5;';
    },

    async selectDisagreeOption(dialog, savedParams, mask) {
      const MAX_WAIT_TIME = 10000;
      const POLL_INTERVAL = 300;

      try {
        // 优先尝试Vue实例方式
        if (await this.trySelectViaVue(dialog, savedParams, mask)) {
          return true;
        }

        // 备用DOM操作方式
        return await this.trySelectViaDOM(dialog, savedParams, mask, MAX_WAIT_TIME, POLL_INTERVAL);
      } catch (error) {
        plugin.error('选择不同意选项出错:', error);
        return false;
      }
    },

    async trySelectViaVue(dialog, savedParams, mask) {
      const radioGroup = dialog.querySelector('.el-radio-group');
      if (!radioGroup || !radioGroup.__vue__) return false;

      const vm = radioGroup.__vue__;
      const disagreeOption = vm.$children.find(child => child.$attrs.value === '2');
      if (!disagreeOption) return false;

      disagreeOption.currentValue = true;
      vm.$emit('change', '2');

      // 等待UI更新
      await new Promise(resolve => setTimeout(resolve, 100));
      disagreeOption.$el.click();

      // 验证是否选中
      await new Promise(resolve => setTimeout(resolve, 50));

      if (disagreeOption.currentValue) {
        this.restoreSavedParams(savedParams);
        return true;
      }
      return false;
    },

    async trySelectViaDOM(dialog, savedParams, mask, maxWaitTime, pollInterval) {
      const startTime = Date.now();

      while (Date.now() - startTime < maxWaitTime) {
        const radioGroup = dialog.querySelector('.el-radio-group');
        if (!radioGroup) {
          await new Promise(resolve => setTimeout(resolve, pollInterval));
          continue;
        }

        const disagreeInput = radioGroup.querySelector('input[value="2"]');
        if (!disagreeInput) {
          await new Promise(resolve => setTimeout(resolve, pollInterval));
          continue;
        }

        // 模拟点击
        ['mousedown', 'mouseup', 'click'].forEach(type => {
          disagreeInput.dispatchEvent(new MouseEvent(type, {
            bubbles: true,
            cancelable: true,
            view: window
          }));
        });

        // 等待UI更新
        await new Promise(resolve => setTimeout(resolve, 50));

        // 检查是否选中
        const isSelected = disagreeInput.checked ||
            disagreeInput.closest('.el-radio')?.classList.contains('is-checked');

        if (isSelected) {
          this.restoreSavedParams(savedParams);
          return true;
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }

      return false;
    },

    restoreSavedParams(params) {
      this.taskId = params.taskId;
      this.projectCode = params.projectCode;
      this.current = params.current;
      this.userID = params.userID;
    }
  },

  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

}
</script>


<style scoped>
.el-loading-mask {
  background-color: rgba(122, 122, 122, .8) !important;
  top: 0 !important;
  left: 0 !important;
  width: 100%;
  height: 100%;
}

.el-loading-text {
  color: #337dff !important;
}

.custom-error-table-dialog .el-dialog__header {
  padding: 20px;
  background-color: #f9fafc;
  border-bottom: 1px solid #e4e7ed;
  font-weight: bold;
}

.custom-error-table-dialog .el-dialog__body {
  padding: 10px 20px 20px 20px;
}

.eldialogIndex {
  z-index: 9999 !important;
}

</style>
