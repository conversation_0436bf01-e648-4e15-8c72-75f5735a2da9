<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
  </div>
</template>

<script>
import { getCompleteTheReportList } from '@/api/mmatTransCreateApp'; // 引入重构后的API
import {Message, MessageBox} from 'element-ui'
import {logValidation, queryWhiteList} from "@/api/commonApi";
import {EamLoading} from "@/util/EamElementExt";
import submitMixin from "@/util/submitMixin";
export default {
  mixins: [submitMixin],
  name: 'App',
  data() {
    return {
      showTip: true,
      taskCode: "",
      projectCode: "",
      errorCode: [],
      whiteList : [],
      result: '',
    }
  },
  mounted() {
    // 自定义绑定确定按钮
    this.initSubmitListener({
      validate: this.validateForm,
      polling: false,
      buttonText: '确 定',
      containerSelector: [".el-dialog .el-dialog__footer .ftBox"],
    });
  },
  created() {
  },
  methods: {
    // 获取审批结果
    async getApprovalResult() {
      const selectedLabel = document.querySelector('label.el-radio.is-checked .el-radio__label');
      if (selectedLabel) {
        const text = selectedLabel.textContent.trim();
        console.log('审批结果:', text);
      } else {
        console.log('未找到调拨类型元素');
      }
    },
    // 获取项目间调拨
    async checkAllocationType() {
      const labels = document.querySelectorAll('.el-form-item__label');
      let allocationLabel = null;

      // 查找“调拨类型”标签
      labels.forEach(label => {
        const labelContent = label.querySelector('.order_form_label');
        if (labelContent && labelContent.textContent.trim() === '调拨类型') {
          allocationLabel = label;
        }
      });

      if (allocationLabel) {
        const contentDiv = allocationLabel.closest('.el-form-item').querySelector('.order_form_content');
        if (contentDiv) {
          const text = contentDiv.textContent.trim();
          if (text === '项目间调拨') {
            this.allocationType = '项目间调拨';
          } else if (text === '项目内调拨') {
            this.allocationType = '项目内调拨';
          } else {
            this.allocationType = '未知类型';
          }
        }
      } else {
        console.warn('未找到调拨类型字段');
        this.allocationType = null;
      }

      console.log('调拨类型:', this.allocationType);
    },
    // 调入项目编码
    async findProjectCode() {
      const labels = document.querySelectorAll('.el-form-item__label');
      let transferInLabel = null;

      // 查找“调入项目编码”标签
      labels.forEach(label => {
        const labelContent = label.querySelector('.order_form_label');
        if (labelContent && labelContent.textContent.trim() === '调入项目编码') {
          transferInLabel = label;
        }
      });

      if (transferInLabel) {
        const contentDiv = transferInLabel.closest('.el-form-item').querySelector('.order_form_content span');
        if (contentDiv) {
          this.projectCode = contentDiv.textContent.trim();
          console.log('项目编码:', this.projectCode);

          // 查询白名单
          await queryWhiteList('MMAT_TRANS_APPROVE_APP', this.projectCode).then(res => {
            console.log('白名单:', this.whiteList);
            this.whiteList = res;
          });
        }
      } else {
        console.warn('未找到调入项目编码字段');
        this.projectCode = null;
      }
    },
    // 获取表格任务编号
    getSelectedTableRows() {
      const rows = [];
      const table = document.querySelector('.el-table__body-wrapper .el-table__body');
      if (!table) return rows;

      // 获取所有行
      const allRows = table.querySelectorAll('tr.el-table__row');

      allRows.forEach(row => {
        // 检查当前行是否未被勾选
        rows.push(row);
      });
      return rows;
    },
    // 获取调入任务编号table 所在列序号
    getColumnIndex() {
      // 获取所有表头单元格
      const headerCells = document.querySelectorAll('.el-table__header .has-gutter tr th');
      // 遍历查找包含"调入任务编码"的单元格
      let targetIndex = -1;
      headerCells.forEach((cell, index) => {
        const cellText = cell.querySelector('.cell')?.textContent?.trim();
        if (cellText && cellText.includes('调入任务编码')) {
          targetIndex = index;
        }
      });

      // 返回结果（注意：索引从0开始）
      console.log('调入任务编码所在列的序号:', targetIndex);
      return targetIndex;
    },
    // 获取调入任务编号
    async getTransferInTaskCode(row, columnIndex) {
      const cells = row.querySelectorAll('td');
      if (cells.length <= columnIndex) return null;

      const cell = cells[columnIndex];

      // 查找 span.el-tooltip.item
      const span = cell.querySelector('span.el-tooltip.item');

      if (!span) {
        console.warn('未找到任务编码对应的 span 元素');
        return null;
      }

      let taskCode = span.textContent.trim();

      // 如果 textContent 为空，尝试读取 title 属性（tooltip 内容可能放在这里）
      if (!taskCode) {
        taskCode = span.title.trim();
      }

      console.log('获取到任务编码:', taskCode);
      return taskCode;
    },
    // 校验
    async validateForm() {
      // 1. 检查调拨类型、项目间调拨、获取白名单
      const columnIndex = this.getColumnIndex();
      console.log('columnIndex:', columnIndex);
      await this.checkAllocationType();
      await this.findProjectCode();
      await this.getApprovalResult();

      // 修复条件判断：只有项目间调拨且有项目编码时才校验
      if (this.allocationType !== '项目间调拨' || !this.showTip || this.result === '不同意') {
        return true;
      }

      try {
        EamLoading.service();
        // 2. 获取选中行并校验任务编码
        const selectedRows = this.getSelectedTableRows();
        const validatePromises = selectedRows.map(async row => {
          const taskCode = await this.getTransferInTaskCode(row, columnIndex);
          return taskCode ? await this.validateTaskCode(taskCode) : Promise.resolve(true);
        });

        // 3. 校验结果处理
        const results = await Promise.all(validatePromises);
        if (results.every(valid => valid)) {
          return true
        } else {
          Message.warning(this.errorCode.join(", ")+ `调入任务编码已完工，请退回申请`);
        }
      } finally {
        EamLoading.close();
      }
      return false;
    },
    /**
     * 校验任务编码（修改后带日志记录版本）
     * @param {string} taskCode 需要校验的任务编码
     * @returns {Promise<boolean>} 返回校验结果
     */
    async validateTaskCode(taskCode) {
      const DOCUMENT_STATUS_APPROVED = '02';  // 完工状态码
      let validationResult = false;          // 默认校验失败
      let responseData = null;               // 记录接口返回数据
      let requestParams = null;              // 记录请求参数

      try {
        // 判断任务编号是否在白名单中
        if (Array.isArray(this.whiteList) && this.whiteList.length > 0) {
          const isWhitelisted = this.whiteList.some(item => {
            // 确保 item.taskCode 存在且为字符串
            return item.taskCode && item.taskCode.includes(taskCode);
          });
          console.log(`任务编号:${taskCode}`);
          if (isWhitelisted) {
            validationResult = true;
            console.log(`任务编号:${taskCode}在白名单中`);
          }
        }

        // 继续请求三方接口进行判断
        if (!validationResult) {
          // 准备请求参数
          requestParams = {
            projectCode: this.projectCode,
            selectDefault: 'PIU_IT_TZJHGLY',
            sysDataAuthQueryDetailVo: {
              companyCode: [],
              secondOrgCode: [],
              key: 'PIU_IT_TZJHGLY',
              value: ''
            },
            checkedKey: [],
            secondOrgCode: [],
            size: 100,
            page: 1,
            taskcode: taskCode
          };

          // 调用校验接口
          await getCompleteTheReportList(requestParams).then(orderData => {
            console.log('接口返回数据:', orderData);
            responseData = orderData;  // 保存接口返回数据
            // 解析接口返回
            const { data } = orderData || {};
            const { body } = data || {};
            const { completionReportList } = body || {};
            const firstItem = Array.isArray(completionReportList) ? completionReportList[0] : null;
            console.log('firstItem:', firstItem);
            // 判断校验结果（02表示已完工，不允许调拨）
            if (firstItem && firstItem.documentStatus === DOCUMENT_STATUS_APPROVED) {
              this.errorCode.push(taskCode);  // 记录错误编码
              validationResult = false;
            } else {
              validationResult = true;
            }
          })

        }
      } catch (error) {
        // 异常处理
        this.errorCode.push(taskCode);
        validationResult = true;
        console.error(`任务编码 ${taskCode} 验证失败:`, error);
      } finally {
        // 无论成功失败都记录日志
        await logValidation({
          businessType: 'MMAT_TRANS_CREATE_APP',
          taskCodes: taskCode,
          projectCode: this.projectCode,
          validationResults: validationResult,
          requestParams: JSON.stringify(requestParams),
          responseData: JSON.stringify(responseData),
          isSuccessful: validationResult,
          errorMessages: validationResult ? '' : `任务编码 ${taskCode} 已完工`,
          pageUrl: window.location.href
        });
      }

      return validationResult;
    },
  }
}
</script>
<style>
.eams-chrome-ext-loading {
  z-index: 99999 !important;
}
.el-message {
  z-index: 9147483648 !important;
}
</style>