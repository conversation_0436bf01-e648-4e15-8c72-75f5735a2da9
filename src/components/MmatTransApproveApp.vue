<template>
  <div>
    <el-tag v-if="showTip" style="position: fixed;right: 50px;top:100px;height:30px;line-height:30px;cursor: pointer;user-select: none;z-index: 9999999999">
      数智赋能平台自动化工具<span @click="showTip=false">(点击关闭)</span>
    </el-tag>
  </div>
</template>

<script>
import submitMixin from "@/util/submitMixin";
import validateConfirm from '@/validate/mmatTrans/approve/validateConfirm'

export default {
  mixins: [submitMixin],
  name: 'App',
  data() {
    return {
      showTip: true,
      taskCode: "",
      projectCode: "",
      errorCode: [],
      whiteList : [],
      result: '',
    }
  },
  mounted() {
    this.initSubmitListener([
      {
        key: 'confirm',
        validate: validateConfirm, // confirm 按钮专用校验
        polling: false,
        buttonText: '确 定',
        containerSelector: [".el-dialog .el-dialog__footer .ftBox"]
      }
    ]);
  }
}
</script>
<style>
.eams-chrome-ext-loading {
  z-index: 99999 !important;
}
.el-message {
  z-index: 9147483648 !important;
}
</style>