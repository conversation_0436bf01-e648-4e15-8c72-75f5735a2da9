<template>
  <div class="base_view border_bottom">
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: 'ViewTitle',
  data() {
    return {}
  },
  methods: {}
}
</script>
<style scoped>
.base_view {
  font-size: 16px;
  position: relative;
  color: #337DFF;
  line-height: 40px;
  padding-left: 10px;
}

.base_view:before {
  content: '';
  display: block;
  position: absolute;
  width: 3px;
  height: 18px;
  background: #4768f3;
  top: 12px;
  left: 0;
}

.border_bottom {
  border-bottom: 1px solid #D9D9D9;
  margin-bottom: 10px;
}
</style>
