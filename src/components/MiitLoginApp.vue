<template>
  <div>
    <component
        v-for="item in componentList"
        :is="item.name"
        :key="item.key"
        v-bind="item.props"
        @close="closeDialog(item.key)"
    />
  </div>
</template>

<script>
import submitMixin from "@/util/submitMixin";
import validateSubmit from "@/validate/miitLogin/login/validateSubmit";
import miitLoginDialog from "@/components/eldialog/miitLoginDialog.vue";

export default {
  name: 'MiitLoginApp',
  mixins: [submitMixin],
  components: {
    miitLoginDialog
  },
  data() {
    return {
      componentList: []
    }
  },
  mounted() {
    this.initSubmitListener([
      {
        key: 'submit',
        validate: (context) => validateSubmit({
          ...context,
          showDialog: this.showDialog
        }),
        buttonText: '自动同步质监申报数据',
        containerSelector: [".operate-btn"]
      }
    ]);
  },
  methods: {
    /**
     * 显示对话框
     * @param {string} dialogName - 对话框组件名
     * @param {Object} dialogProps - 对话框属性
     */
    showDialog(dialogName, dialogProps) {
      this.componentList = [{
        name: dialogName,
        key: dialogName,
        props: dialogProps
      }];
    },

    /**
     * 关闭对话框
     * @param {string} key - 对话框key
     */
    closeDialog(key) {
      this.componentList = this.componentList.filter(item => item.key !== key);
    }
  }
};
</script>
