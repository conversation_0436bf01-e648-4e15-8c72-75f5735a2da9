<template>
  <div class="miit-login-container">
    <!-- 触发按钮 -->
    <el-button 
      type="primary" 
      icon="el-icon-user" 
      @click="showLoginDialog"
      :loading="isChecking"
    >
      工信部系统登录
    </el-button>

    <!-- 登录对话框 -->
    <el-dialog
      title="工信部统一身份认证系统登录"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="miit-login-dialog"
    >
      <el-form :model="loginForm" :rules="loginRules" ref="loginForm" label-width="120px">
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="登录名/统一社会信用代码/身份证号"
            :disabled="isProcessing"
            @keyup.enter.native="handleEnterKey"
          >
            <i slot="prefix" class="el-icon-user"></i>
          </el-input>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            type="password"
            v-model="loginForm.password"
            placeholder="请输入密码"
            :disabled="isProcessing"
            show-password
            @keyup.enter.native="handleEnterKey"
          >
            <i slot="prefix" class="el-icon-lock"></i>
          </el-input>
        </el-form-item>

        <el-form-item label="短信验证码" prop="smsCode">
          <el-input
            v-model="loginForm.smsCode"
            placeholder="请先获取验证码"
            :disabled="!smsCodeEnabled || isProcessing"
            @keyup.enter.native="handleLogin"
          >
            <i slot="prefix" class="el-icon-message"></i>
            <el-button
              slot="append"
              :disabled="!canGetSmsCode"
              :loading="isGettingSms"
              @click="getSmsCode"
              type="primary"
            >
              {{ smsButtonText }}
            </el-button>
          </el-input>
        </el-form-item>

        <el-form-item label="目标URL" prop="toUrl">
          <el-input
            v-model="loginForm.toUrl"
            placeholder="登录成功后跳转的URL"
            :disabled="isProcessing"
          >
            <i slot="prefix" class="el-icon-link"></i>
          </el-input>
        </el-form-item>
      </el-form>

      <!-- 状态显示 -->
      <div class="login-status" v-if="statusMessage">
        <el-alert
          :title="statusMessage"
          :type="statusType"
          :closable="false"
          show-icon
        ></el-alert>
      </div>

      <!-- 登录历史记录 -->
      <div class="login-history" v-if="loginHistory.length > 0">
        <el-divider content-position="left">最近登录记录</el-divider>
        <el-select
          v-model="selectedHistory"
          placeholder="选择历史记录快速填充"
          style="width: 100%"
          @change="fillFromHistory"
        >
          <el-option
            v-for="(item, index) in loginHistory"
            :key="index"
            :label="`${item.username} - ${item.toUrl}`"
            :value="index"
          ></el-option>
        </el-select>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" :disabled="isProcessing">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleLogin" 
          :loading="isLogging"
          :disabled="!canLogin"
        >
          登录
        </el-button>
      </div>
    </el-dialog>

    <!-- 结果处理对话框 -->
    <el-dialog
      title="登录结果处理"
      :visible.sync="resultDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="loginResult">
        <el-alert
          :title="loginResult.message"
          :type="loginResult.success ? 'success' : 'warning'"
          show-icon
          :closable="false"
        ></el-alert>

        <!-- 需要完善企业信息 -->
        <div v-if="loginResult.needCompleteInfo" class="result-actions">
          <p>企业需要完善基础信息才能继续使用系统</p>
          <el-button type="primary" @click="handleCompleteInfo">前往完善信息</el-button>
          <el-button @click="handleSkipAndRedirect">跳过并继续</el-button>
        </div>

        <!-- 需要重置密码 -->
        <div v-if="loginResult.needResetPassword" class="result-actions">
          <p>您的密码需要重置</p>
          <el-button type="primary" @click="handleResetPassword">前往重置密码</el-button>
        </div>

        <!-- 需要完成注册 -->
        <div v-if="loginResult.needFinishRegister" class="result-actions">
          <p>请完成注册流程</p>
          <el-button type="primary" @click="handleFinishRegister">完成注册</el-button>
        </div>

        <!-- 需要同意协议 -->
        <div v-if="loginResult.needAgreeProtocol" class="result-actions">
          <p>请同意用户协议后继续</p>
          <el-button type="primary" @click="handleAgreeProtocol">查看协议</el-button>
        </div>

        <!-- 登录成功 -->
        <div v-if="loginResult.success" class="result-actions">
          <p>登录成功！即将跳转到目标页面</p>
          <el-button type="primary" @click="handleRedirect">立即跳转</el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="resultDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import MiitLoginApi from '@/api/miitLoginApi';
import { EamMessage, EamLoading } from '@/util/EamElementExt';

export default {
  name: 'MiitLoginApp',
  data() {
    return {
      // 对话框控制
      dialogVisible: false,
      resultDialogVisible: false,
      
      // 登录表单
      loginForm: {
        username: '',
        password: '',
        smsCode: '',
        toUrl: 'http://txjs.miit.gov.cn'
      },
      
      // 表单验证规则
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少6位', trigger: 'blur' }
        ],
        smsCode: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' },
          { pattern: /^\d{6}$/, message: '验证码应为6位数字', trigger: 'blur' }
        ]
      },
      
      // 状态控制
      isChecking: false,
      isGettingSms: false,
      isLogging: false,
      isProcessing: false,
      
      // 短信验证码相关
      smsCodeEnabled: false,
      smsCountdown: 0,
      smsTimer: null,
      
      // 状态消息
      statusMessage: '',
      statusType: 'info',
      
      // 登录结果
      loginResult: null,
      
      // 历史记录
      loginHistory: [],
      selectedHistory: '',
      
      // API实例
      loginApi: new MiitLoginApi()
    };
  },
  
  computed: {
    canGetSmsCode() {
      return this.loginForm.username && 
             this.loginForm.password && 
             !this.isGettingSms && 
             this.smsCountdown === 0;
    },
    
    canLogin() {
      return this.loginForm.username && 
             this.loginForm.password && 
             this.loginForm.smsCode && 
             !this.isLogging;
    },
    
    smsButtonText() {
      if (this.isGettingSms) {
        return '发送中...';
      } else if (this.smsCountdown > 0) {
        return `${this.smsCountdown}s后重试`;
      } else {
        return '获取验证码';
      }
    }
  },
  
  mounted() {
    this.loadLoginHistory();
  },
  
  beforeDestroy() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer);
    }
  },
  
  methods: {
    /**
     * 显示登录对话框
     */
    showLoginDialog() {
      this.dialogVisible = true;
      this.resetForm();
      this.setStatus('请输入登录信息', 'info');
    },
    
    /**
     * 处理回车键
     */
    handleEnterKey() {
      if (this.canGetSmsCode && !this.smsCodeEnabled) {
        this.getSmsCode();
      } else if (this.canLogin) {
        this.handleLogin();
      }
    },
    
    /**
     * 获取短信验证码
     */
    async getSmsCode() {
      // 表单验证
      const valid = await this.validateFields(['username', 'password']);
      if (!valid) return;
      
      this.isGettingSms = true;
      this.isProcessing = true;
      this.setStatus('正在发送短信验证码...', 'info');
      
      try {
        const result = await this.loginApi.getSmsCode({
          username: this.loginForm.username,
          password: this.loginForm.password
        });
        
        if (result.success) {
          this.setStatus(result.message, 'success');
          this.smsCodeEnabled = true;
          this.startSmsCountdown();
          
          // 聚焦到验证码输入框
          this.$nextTick(() => {
            this.$refs.loginForm.$el.querySelector('input[placeholder*="验证码"]').focus();
          });
          
          EamMessage.success(result.message);
        } else {
          this.setStatus(`发送失败: ${result.message}`, 'error');
          EamMessage.error(result.message);
        }
      } catch (error) {
        console.error('获取短信验证码失败:', error);
        this.setStatus(`发送失败: ${error.message}`, 'error');
        EamMessage.error('获取短信验证码失败');
      } finally {
        this.isGettingSms = false;
        this.isProcessing = false;
      }
    },
    
    /**
     * 执行登录
     */
    async handleLogin() {
      // 表单验证
      const valid = await this.validateForm();
      if (!valid) return;
      
      this.isLogging = true;
      this.isProcessing = true;
      this.setStatus('正在登录...', 'info');
      
      try {
        const result = await this.loginApi.login({
          username: this.loginForm.username,
          password: this.loginForm.password,
          smsCode: this.loginForm.smsCode,
          toUrl: this.loginForm.toUrl
        });
        
        this.loginResult = result;
        
        if (result.success) {
          this.setStatus('登录成功！', 'success');
          this.saveLoginHistory();
          this.dialogVisible = false;
          this.resultDialogVisible = true;
          EamMessage.success('登录成功');
        } else {
          this.setStatus(`登录失败: ${result.message}`, 'error');
          this.resultDialogVisible = true;
          EamMessage.error(result.message);
        }
      } catch (error) {
        console.error('登录失败:', error);
        this.setStatus(`登录失败: ${error.message}`, 'error');
        EamMessage.error('登录失败');
      } finally {
        this.isLogging = false;
        this.isProcessing = false;
      }
    },
    
    /**
     * 表单验证
     */
    async validateForm() {
      try {
        await this.$refs.loginForm.validate();
        return true;
      } catch (error) {
        return false;
      }
    },
    
    /**
     * 验证指定字段
     */
    async validateFields(fields) {
      try {
        await this.$refs.loginForm.validateField(fields);
        return true;
      } catch (error) {
        return false;
      }
    },
    
    /**
     * 设置状态消息
     */
    setStatus(message, type = 'info') {
      this.statusMessage = message;
      this.statusType = type;
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      this.loginForm.smsCode = '';
      this.smsCodeEnabled = false;
      this.statusMessage = '';
      this.smsCountdown = 0;
      if (this.smsTimer) {
        clearInterval(this.smsTimer);
        this.smsTimer = null;
      }
    },
    
    /**
     * 开始短信倒计时
     */
    startSmsCountdown() {
      this.smsCountdown = 60;
      this.smsTimer = setInterval(() => {
        this.smsCountdown--;
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer);
          this.smsTimer = null;
        }
      }, 1000);
    },
    
    /**
     * 保存登录历史
     */
    saveLoginHistory() {
      const historyItem = {
        username: this.loginForm.username,
        toUrl: this.loginForm.toUrl,
        timestamp: new Date().toISOString()
      };
      
      // 避免重复记录
      const exists = this.loginHistory.some(item => 
        item.username === historyItem.username && item.toUrl === historyItem.toUrl
      );
      
      if (!exists) {
        this.loginHistory.unshift(historyItem);
        // 只保留最近5条记录
        this.loginHistory = this.loginHistory.slice(0, 5);
        localStorage.setItem('miitLoginHistory', JSON.stringify(this.loginHistory));
      }
    },
    
    /**
     * 加载登录历史
     */
    loadLoginHistory() {
      try {
        const history = localStorage.getItem('miitLoginHistory');
        if (history) {
          this.loginHistory = JSON.parse(history);
        }
      } catch (error) {
        console.error('加载登录历史失败:', error);
        this.loginHistory = [];
      }
    },
    
    /**
     * 从历史记录填充表单
     */
    fillFromHistory() {
      if (this.selectedHistory !== '') {
        const historyItem = this.loginHistory[this.selectedHistory];
        if (historyItem) {
          this.loginForm.username = historyItem.username;
          this.loginForm.toUrl = historyItem.toUrl;
        }
      }
    },
    
    // ==================== 结果处理方法 ====================
    
    /**
     * 处理完善企业信息
     */
    handleCompleteInfo() {
      if (this.loginResult.completeInfoUrl) {
        window.open(this.loginResult.completeInfoUrl, '_blank');
      }
      if (this.loginResult.redirectUrl) {
        setTimeout(() => {
          this.handleRedirect();
        }, 2000);
      }
    },
    
    /**
     * 跳过并重定向
     */
    handleSkipAndRedirect() {
      if (this.loginResult.redirectUrl) {
        this.handleRedirect();
      } else {
        this.resultDialogVisible = false;
      }
    },
    
    /**
     * 处理重置密码
     */
    handleResetPassword() {
      if (this.loginResult.resetPasswordUrl) {
        const fullUrl = `http://ucenter.miit.gov.cn${this.loginResult.resetPasswordUrl}`;
        window.open(fullUrl, '_blank');
      }
    },
    
    /**
     * 处理完成注册
     */
    handleFinishRegister() {
      if (this.loginResult.userId) {
        const registerUrl = `http://ucenter.miit.gov.cn/register/getFinshRegister.action?userId=${this.loginResult.userId}`;
        window.open(registerUrl, '_blank');
      }
    },
    
    /**
     * 处理同意协议
     */
    handleAgreeProtocol() {
      if (this.loginResult.userId) {
        const protocolUrl = `http://ucenter.miit.gov.cn/register/protocol_dialog.jsp?userId=${this.loginResult.userId}`;
        window.open(protocolUrl, '_blank');
      }
    },
    
    /**
     * 处理重定向
     */
    handleRedirect() {
      if (this.loginResult.redirectUrl) {
        EamMessage.success('正在跳转...');
        setTimeout(() => {
          window.location.href = this.loginResult.redirectUrl;
        }, 1000);
      }
      this.resultDialogVisible = false;
    },
    
    /**
     * 清除登录历史
     */
    clearLoginHistory() {
      this.$confirm('确定要清除所有登录历史记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loginHistory = [];
        localStorage.removeItem('miitLoginHistory');
        EamMessage.success('登录历史已清除');
      });
    }
  }
};
</script>

<style scoped>
.miit-login-container {
  display: inline-block;
}

.miit-login-dialog .el-dialog__body {
  padding: 20px;
}

.login-status {
  margin: 15px 0;
}

.login-history {
  margin-top: 20px;
}

.result-actions {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.result-actions p {
  margin-bottom: 15px;
  color: #606266;
}

.result-actions .el-button {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 自定义样式 */
.el-input-group__append .el-button {
  border-left: 0;
}

.el-form-item__label {
  font-weight: 500;
}

.el-alert {
  margin-bottom: 0;
}
</style>
