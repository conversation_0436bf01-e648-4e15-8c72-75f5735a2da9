/**
 * CORS跨域问题解决助手
 * 提供多种方案解决跨域问题
 */

/**
 * 检测当前环境和CORS支持情况
 */
export function detectCorsEnvironment() {
    const env = {
        isDevelopment: process.env.NODE_ENV === 'development',
        isExtension: typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id,
        supportsFetch: typeof fetch !== 'undefined',
        supportsProxy: window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1'),
        currentOrigin: window.location.origin,
        userAgent: navigator.userAgent
    };
    
    console.log('🔍 CORS环境检测:', env);
    return env;
}

/**
 * 创建避免预检请求的简单请求配置
 */
export function createSimpleRequestConfig() {
    return {
        headers: {
            // 只使用简单头部，避免预检请求
            'Content-Type': 'application/x-www-form-urlencoded'
            // 不使用以下头部，因为会触发预检请求：
            // 'X-Requested-With': 'XMLHttpRequest'
            // 'Authorization': 'Bearer token'
            // 自定义头部
        },
        // 简单请求的方法：GET, HEAD, POST
        method: 'POST',
        // 避免发送凭据（在跨域时）
        withCredentials: false
    };
}

/**
 * 使用fetch发送简单请求（避免预检请求）
 */
export async function sendSimpleRequest(url, data, options = {}) {
    const config = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: data instanceof URLSearchParams ? data : new URLSearchParams(data),
        // 根据环境决定credentials策略
        credentials: options.withCredentials ? 'include' : 'same-origin',
        ...options
    };
    
    console.log('📤 发送简单请求:', url, config);
    
    try {
        const response = await fetch(url, config);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const contentType = response.headers.get('content-type');
        let responseData;
        
        if (contentType && contentType.includes('application/json')) {
            responseData = await response.json();
        } else {
            responseData = await response.text();
        }
        
        console.log('📥 收到响应:', response.status, responseData);
        return { data: responseData, status: response.status, headers: response.headers };
        
    } catch (error) {
        console.error('❌ 请求失败:', error);
        throw error;
    }
}

/**
 * 代理请求助手
 */
export class ProxyRequestHelper {
    constructor(proxyPrefix = '/miit-api') {
        this.proxyPrefix = proxyPrefix;
        this.env = detectCorsEnvironment();
    }
    
    /**
     * 构建代理URL
     */
    buildProxyUrl(originalUrl) {
        if (this.env.isDevelopment && this.env.supportsProxy) {
            // 开发环境使用代理
            return originalUrl.replace('http://ucenter.miit.gov.cn', this.proxyPrefix);
        }
        return originalUrl;
    }
    
    /**
     * 发送代理请求
     */
    async sendRequest(url, data, options = {}) {
        const proxyUrl = this.buildProxyUrl(url);
        
        console.log('🔄 代理请求:', {
            original: url,
            proxy: proxyUrl,
            isDevelopment: this.env.isDevelopment
        });
        
        return sendSimpleRequest(proxyUrl, data, options);
    }
    
    /**
     * 测试代理连接
     */
    async testProxyConnection() {
        try {
            const testUrl = this.buildProxyUrl('http://ucenter.miit.gov.cn/login.jsp');
            const response = await fetch(testUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                }
            });
            
            const result = {
                success: response.ok,
                status: response.status,
                statusText: response.statusText,
                url: testUrl
            };
            
            console.log('🧪 代理连接测试:', result);
            return result;
            
        } catch (error) {
            console.error('❌ 代理连接测试失败:', error);
            return {
                success: false,
                error: error.message,
                url: this.buildProxyUrl('http://ucenter.miit.gov.cn/login.jsp')
            };
        }
    }
}

/**
 * JSONP请求助手（用于GET请求）
 */
export function sendJsonpRequest(url, params = {}, callbackName = 'callback') {
    return new Promise((resolve, reject) => {
        // 生成唯一的回调函数名
        const uniqueCallback = `jsonp_callback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 创建全局回调函数
        window[uniqueCallback] = function(data) {
            resolve(data);
            // 清理
            document.head.removeChild(script);
            delete window[uniqueCallback];
        };
        
        // 构建URL
        const queryParams = new URLSearchParams({
            ...params,
            [callbackName]: uniqueCallback
        });
        const fullUrl = `${url}?${queryParams}`;
        
        // 创建script标签
        const script = document.createElement('script');
        script.src = fullUrl;
        script.onerror = () => {
            reject(new Error('JSONP request failed'));
            document.head.removeChild(script);
            delete window[uniqueCallback];
        };
        
        // 设置超时
        setTimeout(() => {
            if (window[uniqueCallback]) {
                reject(new Error('JSONP request timeout'));
                document.head.removeChild(script);
                delete window[uniqueCallback];
            }
        }, 10000);
        
        document.head.appendChild(script);
    });
}

/**
 * 多策略请求发送器
 */
export class MultiStrategyRequester {
    constructor() {
        this.proxyHelper = new ProxyRequestHelper();
        this.strategies = [
            'proxy',      // 代理请求
            'simple',     // 简单请求
            'fetch',      // 原生fetch
            'extension'   // 扩展环境
        ];
    }
    
    /**
     * 尝试多种策略发送请求
     */
    async sendRequest(url, data, options = {}) {
        const env = detectCorsEnvironment();
        let lastError;
        
        for (const strategy of this.strategies) {
            try {
                console.log(`🔄 尝试策略: ${strategy}`);
                
                switch (strategy) {
                    case 'proxy':
                        if (env.isDevelopment && env.supportsProxy) {
                            return await this.proxyHelper.sendRequest(url, data, options);
                        }
                        break;
                        
                    case 'simple':
                        return await sendSimpleRequest(url, data, {
                            ...options,
                            withCredentials: false
                        });
                        
                    case 'fetch':
                        return await sendSimpleRequest(url, data, {
                            ...options,
                            withCredentials: true,
                            mode: 'cors'
                        });
                        
                    case 'extension':
                        if (env.isExtension) {
                            return await this.sendExtensionRequest(url, data, options);
                        }
                        break;
                }
            } catch (error) {
                console.warn(`❌ 策略 ${strategy} 失败:`, error.message);
                lastError = error;
                continue;
            }
        }
        
        throw new Error(`所有请求策略都失败了。最后错误: ${lastError?.message}`);
    }
    
    /**
     * 扩展环境请求
     */
    async sendExtensionRequest(url, data, options = {}) {
        // 在Chrome扩展环境中，可以使用更宽松的CORS策略
        return sendSimpleRequest(url, data, {
            ...options,
            mode: 'cors',
            credentials: 'include'
        });
    }
    
    /**
     * 测试所有策略
     */
    async testAllStrategies(testUrl = 'http://ucenter.miit.gov.cn/login.jsp') {
        const results = {};
        
        for (const strategy of this.strategies) {
            try {
                console.log(`🧪 测试策略: ${strategy}`);
                
                let result;
                switch (strategy) {
                    case 'proxy':
                        result = await this.proxyHelper.testProxyConnection();
                        break;
                    case 'simple':
                    case 'fetch':
                    case 'extension':
                        result = await fetch(testUrl, { method: 'HEAD' })
                            .then(res => ({ success: res.ok, status: res.status }))
                            .catch(err => ({ success: false, error: err.message }));
                        break;
                }
                
                results[strategy] = result;
                
            } catch (error) {
                results[strategy] = {
                    success: false,
                    error: error.message
                };
            }
        }
        
        console.log('🧪 所有策略测试结果:', results);
        return results;
    }
}

/**
 * CORS问题诊断工具
 */
export function diagnoseCorsIssue(error) {
    const diagnosis = {
        type: 'unknown',
        message: error.message,
        suggestions: []
    };
    
    if (error.message.includes('CORS')) {
        diagnosis.type = 'cors';
        diagnosis.suggestions.push('配置服务器代理');
        diagnosis.suggestions.push('使用Chrome扩展环境');
        diagnosis.suggestions.push('移除自定义请求头');
    } else if (error.message.includes('preflight')) {
        diagnosis.type = 'preflight';
        diagnosis.suggestions.push('移除X-Requested-With头部');
        diagnosis.suggestions.push('使用简单的Content-Type');
        diagnosis.suggestions.push('避免使用withCredentials');
    } else if (error.message.includes('Network')) {
        diagnosis.type = 'network';
        diagnosis.suggestions.push('检查网络连接');
        diagnosis.suggestions.push('检查代理配置');
        diagnosis.suggestions.push('检查目标服务器状态');
    }
    
    console.log('🔍 CORS问题诊断:', diagnosis);
    return diagnosis;
}

// 创建全局实例
export const corsHelper = new MultiStrategyRequester();

export default {
    detectCorsEnvironment,
    createSimpleRequestConfig,
    sendSimpleRequest,
    ProxyRequestHelper,
    sendJsonpRequest,
    MultiStrategyRequester,
    diagnoseCorsIssue,
    corsHelper
};
