/**
 * 工信部登录页面直接操作工具
 * 用于在登录页面上直接操作DOM元素
 */
class MiitPageOperator {
    constructor() {
        this.selectors = {
            username: '#name',
            password: '#password', 
            smsCode: '#yznum',
            getSmsButton: '#btn',
            loginButton: '#subbtn',
            form: '#login-form',
            hiddenPassword: '#pwd',
            loginType: '#logintype',
            toUrl: '#toUrl'
        };
        
        this.isCurrentPage = this.checkIfMiitLoginPage();
    }

    /**
     * 检查当前是否为工信部登录页面
     * @returns {boolean}
     */
    checkIfMiitLoginPage() {
        return window.location.href.includes('ucenter.miit.gov.cn') && 
               document.querySelector(this.selectors.form) !== null;
    }

    /**
     * 等待页面加载完成
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<boolean>}
     */
    async waitForPageReady(timeout = 10000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            if (this.checkIfMiitLoginPage()) {
                // 等待所有必要元素加载
                const allElementsReady = Object.values(this.selectors).every(selector => 
                    document.querySelector(selector) !== null
                );
                
                if (allElementsReady) {
                    return true;
                }
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        return false;
    }

    /**
     * 设置用户名
     * @param {string} username - 用户名
     * @returns {boolean} 是否设置成功
     */
    setUsername(username) {
        const element = document.querySelector(this.selectors.username);
        if (element) {
            element.value = username;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            return true;
        }
        return false;
    }

    /**
     * 设置密码
     * @param {string} password - 密码
     * @returns {boolean} 是否设置成功
     */
    setPassword(password) {
        const element = document.querySelector(this.selectors.password);
        if (element) {
            element.value = password;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            return true;
        }
        return false;
    }

    /**
     * 设置短信验证码
     * @param {string} smsCode - 短信验证码
     * @returns {boolean} 是否设置成功
     */
    setSmsCode(smsCode) {
        const element = document.querySelector(this.selectors.smsCode);
        if (element) {
            element.value = smsCode;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            return true;
        }
        return false;
    }

    /**
     * 获取用户名
     * @returns {string} 当前用户名
     */
    getUsername() {
        const element = document.querySelector(this.selectors.username);
        return element ? element.value.trim() : '';
    }

    /**
     * 获取密码
     * @returns {string} 当前密码
     */
    getPassword() {
        const element = document.querySelector(this.selectors.password);
        return element ? element.value.trim() : '';
    }

    /**
     * 获取短信验证码
     * @returns {string} 当前短信验证码
     */
    getSmsCode() {
        const element = document.querySelector(this.selectors.smsCode);
        return element ? element.value.trim() : '';
    }

    /**
     * 点击获取验证码按钮
     * @returns {Promise<boolean>} 是否点击成功
     */
    async clickGetSmsButton() {
        const button = document.querySelector(this.selectors.getSmsButton);
        if (button && !button.disabled) {
            button.click();
            return true;
        }
        return false;
    }

    /**
     * 点击登录按钮
     * @returns {Promise<boolean>} 是否点击成功
     */
    async clickLoginButton() {
        const button = document.querySelector(this.selectors.loginButton);
        if (button && !button.disabled) {
            button.click();
            return true;
        }
        return false;
    }

    /**
     * 填充完整登录信息
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.username - 用户名
     * @param {string} credentials.password - 密码
     * @param {string} [credentials.smsCode] - 短信验证码
     * @returns {boolean} 是否填充成功
     */
    fillLoginForm({ username, password, smsCode }) {
        let success = true;
        
        if (username) {
            success = this.setUsername(username) && success;
        }
        
        if (password) {
            success = this.setPassword(password) && success;
        }
        
        if (smsCode) {
            success = this.setSmsCode(smsCode) && success;
        }
        
        return success;
    }

    /**
     * 自动获取短信验证码
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.username - 用户名
     * @param {string} credentials.password - 密码
     * @returns {Promise<Object>} 操作结果
     */
    async autoGetSmsCode({ username, password }) {
        try {
            if (!this.isCurrentPage) {
                throw new Error('当前页面不是工信部登录页面');
            }

            // 填充用户名和密码
            const fillSuccess = this.fillLoginForm({ username, password });
            if (!fillSuccess) {
                throw new Error('填充登录信息失败');
            }

            // 等待一下确保值已设置
            await new Promise(resolve => setTimeout(resolve, 500));

            // 点击获取验证码按钮
            const clickSuccess = await this.clickGetSmsButton();
            if (!clickSuccess) {
                throw new Error('点击获取验证码按钮失败');
            }

            return {
                success: true,
                message: '已触发获取短信验证码，请查看页面提示'
            };
        } catch (error) {
            console.error('自动获取短信验证码失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 自动登录
     * @param {Object} credentials - 完整登录凭据
     * @param {string} credentials.username - 用户名
     * @param {string} credentials.password - 密码
     * @param {string} credentials.smsCode - 短信验证码
     * @returns {Promise<Object>} 操作结果
     */
    async autoLogin({ username, password, smsCode }) {
        try {
            if (!this.isCurrentPage) {
                throw new Error('当前页面不是工信部登录页面');
            }

            // 填充所有登录信息
            const fillSuccess = this.fillLoginForm({ username, password, smsCode });
            if (!fillSuccess) {
                throw new Error('填充登录信息失败');
            }

            // 等待一下确保值已设置
            await new Promise(resolve => setTimeout(resolve, 500));

            // 点击登录按钮
            const clickSuccess = await this.clickLoginButton();
            if (!clickSuccess) {
                throw new Error('点击登录按钮失败');
            }

            return {
                success: true,
                message: '已触发登录，请等待页面响应'
            };
        } catch (error) {
            console.error('自动登录失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 监听页面变化（登录结果）
     * @param {Function} callback - 回调函数
     * @param {number} timeout - 超时时间
     * @returns {Promise<void>}
     */
    async watchForLoginResult(callback, timeout = 30000) {
        const startTime = Date.now();
        const originalUrl = window.location.href;
        
        const checkInterval = setInterval(() => {
            const currentTime = Date.now();
            const currentUrl = window.location.href;
            
            // 检查是否超时
            if (currentTime - startTime > timeout) {
                clearInterval(checkInterval);
                callback({
                    success: false,
                    message: '登录超时',
                    timeout: true
                });
                return;
            }
            
            // 检查URL是否发生变化（登录成功通常会跳转）
            if (currentUrl !== originalUrl) {
                clearInterval(checkInterval);
                callback({
                    success: true,
                    message: '登录成功，页面已跳转',
                    newUrl: currentUrl
                });
                return;
            }
            
            // 检查页面是否有错误提示
            const errorElements = document.querySelectorAll('.layui-layer-content, .error-message, .alert');
            for (const element of errorElements) {
                if (element.textContent && element.textContent.includes('错误') || 
                    element.textContent.includes('失败') || 
                    element.textContent.includes('验证码')) {
                    clearInterval(checkInterval);
                    callback({
                        success: false,
                        message: element.textContent.trim(),
                        error: true
                    });
                    return;
                }
            }
        }, 1000);
    }

    /**
     * 完整的自动化登录流程
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.username - 用户名
     * @param {string} credentials.password - 密码
     * @param {Function} [onSmsCodeNeeded] - 需要验证码时的回调
     * @returns {Promise<Object>} 登录结果
     */
    async autoLoginFlow({ username, password }, onSmsCodeNeeded) {
        try {
            console.log('开始自动化登录流程...');
            
            // 1. 等待页面准备就绪
            const pageReady = await this.waitForPageReady();
            if (!pageReady) {
                throw new Error('页面加载超时');
            }
            
            // 2. 自动获取短信验证码
            console.log('步骤1: 获取短信验证码');
            const smsResult = await this.autoGetSmsCode({ username, password });
            if (!smsResult.success) {
                throw new Error(smsResult.message);
            }
            
            console.log('短信验证码获取成功，等待用户输入...');
            
            // 3. 等待用户输入验证码
            let smsCode = '';
            if (onSmsCodeNeeded) {
                smsCode = await onSmsCodeNeeded();
            } else {
                smsCode = prompt('请输入收到的短信验证码:');
            }
            
            if (!smsCode) {
                throw new Error('未输入短信验证码');
            }
            
            // 4. 执行登录
            console.log('步骤2: 执行登录');
            const loginResult = await this.autoLogin({ username, password, smsCode });
            if (!loginResult.success) {
                throw new Error(loginResult.message);
            }
            
            // 5. 监听登录结果
            return new Promise((resolve) => {
                this.watchForLoginResult((result) => {
                    resolve(result);
                });
            });
            
        } catch (error) {
            console.error('自动化登录流程失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * 获取页面状态信息
     * @returns {Object} 页面状态
     */
    getPageStatus() {
        return {
            isLoginPage: this.isCurrentPage,
            currentUrl: window.location.href,
            formExists: !!document.querySelector(this.selectors.form),
            elementsStatus: {
                username: !!document.querySelector(this.selectors.username),
                password: !!document.querySelector(this.selectors.password),
                smsCode: !!document.querySelector(this.selectors.smsCode),
                getSmsButton: !!document.querySelector(this.selectors.getSmsButton),
                loginButton: !!document.querySelector(this.selectors.loginButton)
            },
            formValues: {
                username: this.getUsername(),
                password: this.getPassword() ? '***' : '',
                smsCode: this.getSmsCode()
            }
        };
    }

    /**
     * 高亮显示登录表单元素
     * @param {number} duration - 高亮持续时间（毫秒）
     */
    highlightLoginElements(duration = 3000) {
        const elements = [
            this.selectors.username,
            this.selectors.password,
            this.selectors.smsCode,
            this.selectors.getSmsButton,
            this.selectors.loginButton
        ].map(selector => document.querySelector(selector)).filter(el => el);

        elements.forEach(element => {
            const originalStyle = element.style.cssText;
            element.style.cssText += '; border: 2px solid #ff4444 !important; box-shadow: 0 0 10px #ff4444 !important;';
            
            setTimeout(() => {
                element.style.cssText = originalStyle;
            }, duration);
        });
    }

    /**
     * 创建操作面板
     * @returns {HTMLElement} 操作面板元素
     */
    createOperationPanel() {
        // 检查是否已存在面板
        const existingPanel = document.getElementById('miit-operation-panel');
        if (existingPanel) {
            existingPanel.remove();
        }

        const panel = document.createElement('div');
        panel.id = 'miit-operation-panel';
        panel.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 300px;
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                font-family: Arial, sans-serif;
            ">
                <div style="
                    background: #409eff;
                    color: white;
                    padding: 12px 16px;
                    border-radius: 8px 8px 0 0;
                    font-weight: bold;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <span>工信部登录助手</span>
                    <button id="close-panel" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 18px;
                        cursor: pointer;
                        padding: 0;
                        width: 20px;
                        height: 20px;
                    ">×</button>
                </div>
                <div style="padding: 16px;">
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #666;">用户名:</label>
                        <input type="text" id="panel-username" placeholder="登录名/统一社会信用代码/身份证号" style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            font-size: 14px;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #666;">密码:</label>
                        <input type="password" id="panel-password" placeholder="请输入密码" style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            font-size: 14px;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #666;">短信验证码:</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="text" id="panel-sms-code" placeholder="验证码" style="
                                flex: 1;
                                padding: 8px;
                                border: 1px solid #ddd;
                                border-radius: 4px;
                                font-size: 14px;
                            ">
                            <button id="get-sms-btn" style="
                                padding: 8px 12px;
                                background: #67c23a;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 12px;
                                white-space: nowrap;
                            ">获取验证码</button>
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                        <button id="fill-form-btn" style="
                            flex: 1;
                            padding: 10px;
                            background: #409eff;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                        ">填充表单</button>
                        <button id="auto-login-btn" style="
                            flex: 1;
                            padding: 10px;
                            background: #e6a23c;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                        ">自动登录</button>
                    </div>
                    <div style="margin-bottom: 12px;">
                        <button id="highlight-btn" style="
                            width: 100%;
                            padding: 8px;
                            background: #909399;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">高亮显示表单元素</button>
                    </div>
                    <div id="panel-status" style="
                        font-size: 12px;
                        color: #666;
                        background: #f5f7fa;
                        padding: 8px;
                        border-radius: 4px;
                        min-height: 20px;
                    ">
                        准备就绪
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(panel);
        this.bindPanelEvents(panel);
        return panel;
    }

    /**
     * 绑定操作面板事件
     * @param {HTMLElement} panel - 面板元素
     */
    bindPanelEvents(panel) {
        const statusDiv = panel.querySelector('#panel-status');
        const setStatus = (message, color = '#666') => {
            statusDiv.textContent = message;
            statusDiv.style.color = color;
        };

        // 关闭面板
        panel.querySelector('#close-panel').onclick = () => {
            panel.remove();
        };

        // 获取验证码
        panel.querySelector('#get-sms-btn').onclick = async () => {
            const username = panel.querySelector('#panel-username').value.trim();
            const password = panel.querySelector('#panel-password').value.trim();
            
            if (!username || !password) {
                setStatus('请先输入用户名和密码', '#f56c6c');
                return;
            }
            
            setStatus('正在获取验证码...', '#409eff');
            const result = await this.autoGetSmsCode({ username, password });
            
            if (result.success) {
                setStatus('验证码获取成功，请查看手机', '#67c23a');
            } else {
                setStatus(`获取失败: ${result.message}`, '#f56c6c');
            }
        };

        // 填充表单
        panel.querySelector('#fill-form-btn').onclick = () => {
            const username = panel.querySelector('#panel-username').value.trim();
            const password = panel.querySelector('#panel-password').value.trim();
            const smsCode = panel.querySelector('#panel-sms-code').value.trim();
            
            const success = this.fillLoginForm({ username, password, smsCode });
            
            if (success) {
                setStatus('表单填充成功', '#67c23a');
            } else {
                setStatus('表单填充失败', '#f56c6c');
            }
        };

        // 自动登录
        panel.querySelector('#auto-login-btn').onclick = async () => {
            const username = panel.querySelector('#panel-username').value.trim();
            const password = panel.querySelector('#panel-password').value.trim();
            const smsCode = panel.querySelector('#panel-sms-code').value.trim();
            
            if (!username || !password || !smsCode) {
                setStatus('请填写完整信息', '#f56c6c');
                return;
            }
            
            setStatus('正在登录...', '#409eff');
            const result = await this.autoLogin({ username, password, smsCode });
            
            if (result.success) {
                setStatus('登录请求已发送', '#67c23a');
                
                // 监听登录结果
                this.watchForLoginResult((loginResult) => {
                    if (loginResult.success) {
                        setStatus('登录成功！', '#67c23a');
                    } else {
                        setStatus(`登录失败: ${loginResult.message}`, '#f56c6c');
                    }
                });
            } else {
                setStatus(`登录失败: ${result.message}`, '#f56c6c');
            }
        };

        // 高亮元素
        panel.querySelector('#highlight-btn').onclick = () => {
            this.highlightLoginElements();
            setStatus('已高亮显示表单元素', '#409eff');
        };

        // 同步表单值
        const syncFormValues = () => {
            panel.querySelector('#panel-username').value = this.getUsername();
            panel.querySelector('#panel-password').value = this.getPassword();
            panel.querySelector('#panel-sms-code').value = this.getSmsCode();
        };

        // 定期同步表单值
        setInterval(syncFormValues, 2000);
        syncFormValues(); // 初始同步
    }

    /**
     * 显示操作面板
     */
    showOperationPanel() {
        if (!this.isCurrentPage) {
            alert('请先导航到工信部登录页面: https://txjs.miit.gov.cn/sso.action');
            return;
        }
        
        this.createOperationPanel();
    }
}

// 创建全局实例
const miitPageOperator = new MiitPageOperator();

// 如果当前就是登录页面，自动显示操作面板
if (miitPageOperator.isCurrentPage) {
    console.log('检测到工信部登录页面，可以使用 miitPageOperator.showOperationPanel() 显示操作面板');
}

export default miitPageOperator;
export { MiitPageOperator };
