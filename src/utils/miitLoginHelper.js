import MiitLoginApi from '@/validate/miitLogin/utils/miitLoginApi';
import { EamMessage, EamLoading } from '@/util/EamElementExt';

/**
 * 工信部登录助手类
 * 提供更高级的登录功能和用户交互
 */
class MiitLoginHelper {
    constructor() {
        this.loginApi = new MiitLoginApi();
        this.currentLoginData = null;
    }

    /**
     * 显示登录对话框
     * @param {Object} options - 配置选项
     * @param {string} [options.title='工信部系统登录'] - 对话框标题
     * @param {string} [options.toUrl] - 登录成功后跳转URL
     * @param {Function} [options.onSuccess] - 登录成功回调
     * @param {Function} [options.onError] - 登录失败回调
     * @returns {Promise<void>}
     */
    async showLoginDialog(options = {}) {
        const {
            title = '工信部系统登录',
            toUrl,
            onSuccess,
            onError
        } = options;

        // 创建登录表单HTML
        const loginFormHtml = this.createLoginFormHtml();

        // 显示对话框
        layer.open({
            type: 1,
            title: title,
            area: ['450px', '400px'],
            content: loginFormHtml,
            btn: ['获取验证码', '登录', '取消'],
            btn1: async (index, layero) => {
                await this.handleGetSmsCode(layero);
            },
            btn2: async (index, layero) => {
                await this.handleLogin(layero, { toUrl, onSuccess, onError });
            },
            btn3: (index) => {
                layer.close(index);
            },
            success: (layero) => {
                this.initLoginForm(layero);
            }
        });
    }

    /**
     * 创建登录表单HTML
     * @returns {string} HTML字符串
     */
    createLoginFormHtml() {
        return `
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px;">用户名/统一社会信用代码/身份证号:</label>
                    <input type="text" id="miit-username" placeholder="请输入用户名" 
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px;">密码:</label>
                    <input type="password" id="miit-password" placeholder="请输入密码" 
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px;">短信验证码:</label>
                    <input type="text" id="miit-sms-code" placeholder="请先获取验证码" 
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" disabled>
                </div>
                <div id="miit-login-status" style="margin-top: 10px; color: #666; font-size: 12px;">
                    请先输入用户名和密码，然后获取短信验证码
                </div>
            </div>
        `;
    }

    /**
     * 初始化登录表单
     * @param {jQuery} layero - 对话框jQuery对象
     */
    initLoginForm(layero) {
        const usernameInput = layero.find('#miit-username');
        const passwordInput = layero.find('#miit-password');
        
        // 添加回车键监听
        layero.find('input').on('keypress', (e) => {
            if (e.which === 13) {
                const smsCodeInput = layero.find('#miit-sms-code');
                if (smsCodeInput.val()) {
                    // 如果已有验证码，直接登录
                    layero.find('.layui-layer-btn0').click();
                } else {
                    // 否则获取验证码
                    layero.find('.layui-layer-btn1').click();
                }
            }
        });
    }

    /**
     * 处理获取短信验证码
     * @param {jQuery} layero - 对话框jQuery对象
     */
    async handleGetSmsCode(layero) {
        const username = layero.find('#miit-username').val().trim();
        const password = layero.find('#miit-password').val().trim();
        const statusDiv = layero.find('#miit-login-status');

        if (!username || !password) {
            EamMessage.warning('请先输入用户名和密码');
            return;
        }

        try {
            EamLoading.service('正在发送短信验证码...');
            statusDiv.text('正在发送短信验证码...');

            const result = await this.loginApi.getSmsCode({ username, password });

            if (result.success) {
                EamMessage.success(result.message);
                statusDiv.html(`<span style="color: green;">${result.message}</span>`);
                
                // 启用验证码输入框
                layero.find('#miit-sms-code').prop('disabled', false).focus();
                
                // 保存登录数据供后续使用
                this.currentLoginData = { username, password };
            } else {
                EamMessage.error(result.message);
                statusDiv.html(`<span style="color: red;">发送失败: ${result.message}</span>`);
            }
        } catch (error) {
            console.error('获取短信验证码失败:', error);
            EamMessage.error('获取短信验证码失败');
            statusDiv.html(`<span style="color: red;">发送失败: ${error.message}</span>`);
        } finally {
            EamLoading.close();
        }
    }

    /**
     * 处理登录
     * @param {jQuery} layero - 对话框jQuery对象
     * @param {Object} options - 选项
     */
    async handleLogin(layero, options = {}) {
        const { toUrl, onSuccess, onError } = options;
        const username = layero.find('#miit-username').val().trim();
        const password = layero.find('#miit-password').val().trim();
        const smsCode = layero.find('#miit-sms-code').val().trim();
        const statusDiv = layero.find('#miit-login-status');

        if (!username || !password || !smsCode) {
            EamMessage.warning('请填写完整的登录信息');
            return;
        }

        try {
            EamLoading.service('正在登录...');
            statusDiv.text('正在登录...');

            const result = await this.loginApi.login({
                username,
                password,
                smsCode,
                toUrl
            });

            if (result.success) {
                EamMessage.success('登录成功');
                statusDiv.html(`<span style="color: green;">登录成功，正在跳转...</span>`);
                
                // 关闭对话框
                layer.closeAll();
                
                // 执行成功回调或跳转
                if (onSuccess) {
                    onSuccess(result);
                } else if (result.redirectUrl) {
                    window.location.href = result.redirectUrl;
                }
            } else {
                this.handleLoginError(result, statusDiv, onError);
            }
        } catch (error) {
            console.error('登录失败:', error);
            EamMessage.error('登录失败');
            statusDiv.html(`<span style="color: red;">登录失败: ${error.message}</span>`);
            
            if (onError) {
                onError(error);
            }
        } finally {
            EamLoading.close();
        }
    }

    /**
     * 处理登录错误
     * @param {Object} result - 登录结果
     * @param {jQuery} statusDiv - 状态显示元素
     * @param {Function} onError - 错误回调
     */
    handleLoginError(result, statusDiv, onError) {
        let message = result.message;
        let action = null;

        if (result.needCompleteInfo) {
            message += '，是否前往完善信息？';
            action = () => {
                if (confirm(message)) {
                    window.open(result.completeInfoUrl);
                }
                if (result.redirectUrl) {
                    window.location.href = result.redirectUrl;
                }
            };
        } else if (result.needResetPassword) {
            message += '，是否前往重置密码？';
            action = () => {
                if (confirm(message)) {
                    window.location.href = result.resetPasswordUrl;
                }
            };
        }

        EamMessage.error(message);
        statusDiv.html(`<span style="color: red;">${result.message}</span>`);

        if (action) {
            setTimeout(action, 1000);
        }

        if (onError) {
            onError(result);
        }
    }

    /**
     * 快速登录方法（用于自动化脚本）
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.username - 用户名
     * @param {string} credentials.password - 密码
     * @param {string} [credentials.toUrl] - 目标URL
     * @returns {Promise<Object>} 登录结果
     */
    async quickLogin(credentials) {
        try {
            EamLoading.service('正在执行登录流程...');
            
            // 开始登录流程
            const flowResult = await this.loginApi.loginFlow(credentials);
            
            if (!flowResult.success) {
                throw new Error(flowResult.message);
            }

            EamMessage.success(flowResult.message);
            
            return {
                success: true,
                message: '短信验证码已发送，请手动输入验证码完成登录',
                loginFunction: flowResult.loginFunction
            };
            
        } catch (error) {
            console.error('快速登录失败:', error);
            EamMessage.error(`登录失败: ${error.message}`);
            return {
                success: false,
                message: error.message,
                error: error
            };
        } finally {
            EamLoading.close();
        }
    }

    /**
     * 检查登录状态
     * @returns {Promise<Object>} 登录状态
     */
    async checkLoginStatus() {
        try {
            const result = await this.loginApi.getLoginPageInfo();
            return result;
        } catch (error) {
            console.error('检查登录状态失败:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }
}

// 创建全局实例
const miitLoginHelper = new MiitLoginHelper();

export default miitLoginHelper;
export { MiitLoginHelper };
