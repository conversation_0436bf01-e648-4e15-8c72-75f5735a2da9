<template>
  <div class="extension-api-test">
    <el-card class="test-card">
      <div slot="header" class="card-header">
        <span>🔌 浏览器插件API测试</span>
        <el-tag :type="connectionStatus.type">{{ connectionStatus.text }}</el-tag>
      </div>

      <!-- 环境信息 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="24">
          <el-alert
            title="浏览器插件环境"
            type="info"
            :closable="false"
            show-icon
          >
            <p><strong>直接访问:</strong> http://ucenter.miit.gov.cn (无需代理)</p>
            <p><strong>CORS策略:</strong> 浏览器插件可以跨域访问</p>
            <p><strong>当前环境:</strong> {{ environmentInfo }}</p>
          </el-alert>
        </el-col>
      </el-row>

      <!-- 测试按钮组 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
          <el-button 
            type="primary" 
            @click="testConnection" 
            :loading="testing.connection"
            icon="el-icon-connection"
            style="width: 100%;"
          >
            测试连接
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button 
            type="success" 
            @click="testSmsApi" 
            :loading="testing.sms"
            icon="el-icon-message"
            style="width: 100%;"
          >
            测试短信API
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button 
            type="warning" 
            @click="testLoginApi" 
            :loading="testing.login"
            icon="el-icon-user"
            style="width: 100%;"
          >
            测试登录API
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button 
            type="info" 
            @click="runFullTest" 
            :loading="testing.full"
            icon="el-icon-video-play"
            style="width: 100%;"
          >
            完整测试
          </el-button>
        </el-col>
      </el-row>

      <!-- 登录表单 -->
      <el-form :model="testForm" :rules="testRules" ref="testForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="testForm.username"
                placeholder="测试用户名（不会真实发送）"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                type="password"
                v-model="testForm.password"
                placeholder="测试密码"
                show-password
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="短信验证码" prop="smsCode">
              <el-input
                v-model="testForm.smsCode"
                placeholder="测试验证码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标URL" prop="toUrl">
              <el-input
                v-model="testForm.toUrl"
                placeholder="跳转URL"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 测试结果 -->
      <el-divider content-position="center">测试结果</el-divider>
      <div class="test-results">
        <el-timeline>
          <el-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :timestamp="result.timestamp"
            :type="result.type"
            :icon="result.icon"
          >
            <el-card>
              <h4>{{ result.title }}</h4>
              <p>{{ result.message }}</p>
              <div v-if="result.data" class="result-data">
                <el-button 
                  type="text" 
                  @click="showResultData(result.data)"
                  icon="el-icon-view"
                >
                  查看详细数据
                </el-button>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <!-- 数据查看对话框 -->
    <el-dialog
      title="详细数据"
      :visible.sync="dataDialogVisible"
      width="60%"
    >
      <pre class="json-data">{{ formatJson(selectedData) }}</pre>
    </el-dialog>
  </div>
</template>

<script>
import MiitLoginApiExtension from '@/validate/miitLogin/utils/miitLoginApiExtension';

export default {
  name: 'ExtensionApiTest',
  data() {
    return {
      // 测试表单
      testForm: {
        username: 'test-user',
        password: 'test-password',
        smsCode: '123456',
        toUrl: 'http://txjs.miit.gov.cn'
      },
      
      // 表单验证规则
      testRules: {
        username: [
          { required: true, message: '请输入测试用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入测试密码', trigger: 'blur' }
        ]
      },
      
      // 测试状态
      testing: {
        connection: false,
        sms: false,
        login: false,
        full: false
      },
      
      // 连接状态
      connectionStatus: {
        type: 'info',
        text: '未测试'
      },
      
      // 测试结果
      testResults: [],
      
      // 对话框控制
      dataDialogVisible: false,
      selectedData: null,
      
      // 环境信息
      environmentInfo: '',
      
      // API实例
      loginApi: new MiitLoginApiExtension()
    };
  },
  
  mounted() {
    this.detectEnvironment();
    this.addTestResult('info', '测试页面已加载', '浏览器插件API测试环境已准备就绪', 'el-icon-info');
  },
  
  methods: {
    /**
     * 检测环境信息
     */
    detectEnvironment() {
      const isExtension = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
      const origin = window.location.origin;
      const userAgent = navigator.userAgent;
      
      this.environmentInfo = `${isExtension ? '浏览器插件' : '普通网页'} | ${origin}`;
      
      if (!isExtension) {
        this.addTestResult('warning', '环境提醒', '当前不在浏览器插件环境中，可能会遇到CORS限制', 'el-icon-warning');
      }
    },
    
    /**
     * 测试连接
     */
    async testConnection() {
      this.testing.connection = true;
      
      try {
        const result = await this.loginApi.testConnection();
        
        if (result.success) {
          this.connectionStatus = { type: 'success', text: '连接正常' };
          this.addTestResult('success', '连接测试通过', result.message, 'el-icon-success', result);
        } else {
          this.connectionStatus = { type: 'danger', text: '连接失败' };
          this.addTestResult('danger', '连接测试失败', result.message, 'el-icon-error', result);
        }
      } catch (error) {
        console.error('连接测试失败:', error);
        this.connectionStatus = { type: 'danger', text: '连接异常' };
        this.addTestResult('danger', '连接测试异常', error.message, 'el-icon-error');
      } finally {
        this.testing.connection = false;
      }
    },
    
    /**
     * 测试短信API
     */
    async testSmsApi() {
      this.testing.sms = true;
      
      try {
        const result = await this.loginApi.getSmsCode({
          username: this.testForm.username,
          password: this.testForm.password
        });
        
        if (result.success) {
          this.addTestResult('success', '短信API测试通过', result.message, 'el-icon-message', result);
        } else {
          this.addTestResult('warning', '短信API返回错误', result.message, 'el-icon-warning', result);
        }
      } catch (error) {
        console.error('短信API测试失败:', error);
        this.addTestResult('danger', '短信API测试失败', error.message, 'el-icon-error');
      } finally {
        this.testing.sms = false;
      }
    },
    
    /**
     * 测试登录API
     */
    async testLoginApi() {
      this.testing.login = true;
      
      try {
        const result = await this.loginApi.login({
          username: this.testForm.username,
          password: this.testForm.password,
          smsCode: this.testForm.smsCode,
          toUrl: this.testForm.toUrl
        });
        
        if (result.success) {
          this.addTestResult('success', '登录API测试通过', result.message, 'el-icon-user', result);
        } else {
          this.addTestResult('warning', '登录API返回错误', result.message, 'el-icon-warning', result);
        }
      } catch (error) {
        console.error('登录API测试失败:', error);
        this.addTestResult('danger', '登录API测试失败', error.message, 'el-icon-error');
      } finally {
        this.testing.login = false;
      }
    },
    
    /**
     * 运行完整测试
     */
    async runFullTest() {
      this.testing.full = true;
      this.testResults = [];
      
      try {
        this.addTestResult('info', '开始完整测试', '正在运行所有测试项目...', 'el-icon-loading');
        
        // 步骤1: 连接测试
        await this.testConnection();
        await this.delay(1000);
        
        // 步骤2: 短信API测试
        await this.testSmsApi();
        await this.delay(1000);
        
        // 步骤3: 登录API测试
        await this.testLoginApi();
        
        // 完成
        this.addTestResult('success', '完整测试完成', '所有API测试已完成，请查看详细结果', 'el-icon-circle-check');
        
      } catch (error) {
        console.error('完整测试失败:', error);
        this.addTestResult('danger', '完整测试失败', error.message, 'el-icon-error');
      } finally {
        this.testing.full = false;
      }
    },
    
    /**
     * 添加测试结果
     */
    addTestResult(type, title, message, icon, data = null) {
      this.testResults.push({
        type: type,
        title: title,
        message: message,
        icon: icon,
        data: data,
        timestamp: new Date().toLocaleTimeString()
      });
    },
    
    /**
     * 显示结果数据
     */
    showResultData(data) {
      this.selectedData = data;
      this.dataDialogVisible = true;
    },
    
    /**
     * 格式化JSON数据
     */
    formatJson(data) {
      return JSON.stringify(data, null, 2);
    },
    
    /**
     * 延迟函数
     */
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  }
};
</script>

<style scoped>
.extension-api-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.test-results {
  max-height: 500px;
  overflow-y: auto;
}

.result-data {
  margin-top: 10px;
}

.json-data {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
}

.el-timeline-item__content {
  padding-bottom: 20px;
}

.el-card {
  margin-bottom: 0;
}
</style>
