<template>
  <div class="miit-login-test">
    <el-card class="test-card">
      <div slot="header" class="card-header">
        <span>🔐 工信部登录API测试</span>
        <el-tag :type="connectionStatus.type">{{ connectionStatus.text }}</el-tag>
      </div>

      <!-- 连接测试 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="24">
          <el-alert
            title="测试说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>此页面用于测试工信部登录API的跨域解决方案。请确保已按照文档配置代理。</p>
            <p><strong>代理地址:</strong> /miit-api/* → http://ucenter.miit.gov.cn/*</p>
          </el-alert>
        </el-col>
      </el-row>

      <!-- 测试按钮组 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="8">
          <el-button 
            type="primary" 
            @click="testConnection" 
            :loading="testing.connection"
            icon="el-icon-connection"
            style="width: 100%;"
          >
            测试连接
          </el-button>
        </el-col>
        <el-col :span="8">
          <el-button 
            type="success" 
            @click="testSmsApi" 
            :loading="testing.sms"
            icon="el-icon-message"
            style="width: 100%;"
          >
            测试短信API
          </el-button>
        </el-col>
        <el-col :span="8">
          <el-button 
            type="warning" 
            @click="testLoginApi" 
            :loading="testing.login"
            icon="el-icon-user"
            style="width: 100%;"
          >
            测试登录API
          </el-button>
        </el-col>
      </el-row>

      <!-- 登录表单 -->
      <el-form :model="testForm" :rules="testRules" ref="testForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="testForm.username"
                placeholder="测试用户名（不会真实发送）"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                type="password"
                v-model="testForm.password"
                placeholder="测试密码"
                show-password
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="短信验证码" prop="smsCode">
              <el-input
                v-model="testForm.smsCode"
                placeholder="测试验证码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标URL" prop="toUrl">
              <el-input
                v-model="testForm.toUrl"
                placeholder="跳转URL"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 完整测试流程 -->
      <el-divider content-position="center">完整测试流程</el-divider>
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="连接测试" description="测试代理配置"></el-step>
        <el-step title="获取验证码" description="测试短信API"></el-step>
        <el-step title="模拟登录" description="测试登录API"></el-step>
        <el-step title="完成" description="所有测试通过"></el-step>
      </el-steps>

      <div style="text-align: center; margin: 20px 0;">
        <el-button 
          type="primary" 
          @click="runFullTest" 
          :loading="testing.full"
          icon="el-icon-video-play"
          size="medium"
        >
          运行完整测试
        </el-button>
        <el-button @click="resetTest" icon="el-icon-refresh">重置测试</el-button>
      </div>

      <!-- 测试结果 -->
      <el-divider content-position="center">测试结果</el-divider>
      <div class="test-results">
        <el-timeline>
          <el-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :timestamp="result.timestamp"
            :type="result.type"
            :icon="result.icon"
          >
            <el-card>
              <h4>{{ result.title }}</h4>
              <p>{{ result.message }}</p>
              <div v-if="result.data" class="result-data">
                <el-button 
                  type="text" 
                  @click="showResultData(result.data)"
                  icon="el-icon-view"
                >
                  查看详细数据
                </el-button>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <!-- 数据查看对话框 -->
    <el-dialog
      title="详细数据"
      :visible.sync="dataDialogVisible"
      width="60%"
    >
      <pre class="json-data">{{ formatJson(selectedData) }}</pre>
    </el-dialog>

    <!-- 使用说明对话框 -->
    <el-dialog
      title="使用说明"
      :visible.sync="helpDialogVisible"
      width="70%"
    >
      <div class="help-content">
        <h3>🚀 快速开始</h3>
        <ol>
          <li>确保已配置vue.config.js中的代理设置</li>
          <li>重启开发服务器 (npm run serve)</li>
          <li>点击"测试连接"验证代理是否正常工作</li>
          <li>填写测试用户名和密码（不会真实发送）</li>
          <li>运行完整测试流程</li>
        </ol>

        <h3>⚠️ 注意事项</h3>
        <ul>
          <li>测试数据不会发送到真实服务器</li>
          <li>仅验证API调用和响应格式</li>
          <li>生产环境需要配置服务器代理</li>
        </ul>

        <h3>🔧 故障排除</h3>
        <ul>
          <li><strong>连接失败:</strong> 检查vue.config.js配置和服务器重启</li>
          <li><strong>CORS错误:</strong> 确保代理路径正确 (/miit-api/*)</li>
          <li><strong>网络错误:</strong> 检查目标服务器是否可访问</li>
        </ul>
      </div>
    </el-dialog>

    <!-- 浮动帮助按钮 -->
    <el-button
      type="primary"
      icon="el-icon-question"
      circle
      class="help-button"
      @click="helpDialogVisible = true"
    ></el-button>
  </div>
</template>

<script>
import MiitLoginApi from '@/api/miitLoginApi';

export default {
  name: 'MiitLoginTest',
  data() {
    return {
      // 测试表单
      testForm: {
        username: 'test-user',
        password: 'test-password',
        smsCode: '123456',
        toUrl: 'http://txjs.miit.gov.cn'
      },
      
      // 表单验证规则
      testRules: {
        username: [
          { required: true, message: '请输入测试用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入测试密码', trigger: 'blur' }
        ]
      },
      
      // 测试状态
      testing: {
        connection: false,
        sms: false,
        login: false,
        full: false
      },
      
      // 连接状态
      connectionStatus: {
        type: 'info',
        text: '未测试'
      },
      
      // 测试步骤
      currentStep: 0,
      
      // 测试结果
      testResults: [],
      
      // 对话框控制
      dataDialogVisible: false,
      helpDialogVisible: false,
      selectedData: null,
      
      // API实例
      loginApi: new MiitLoginApi()
    };
  },
  
  mounted() {
    this.addTestResult('info', '测试页面已加载', '准备开始测试工信部登录API', 'el-icon-info');
  },
  
  methods: {
    /**
     * 测试连接
     */
    async testConnection() {
      this.testing.connection = true;
      
      try {
        // 测试代理连接
        const response = await fetch('/miit-api/login.jsp', {
          method: 'GET',
          headers: {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
          }
        });
        
        if (response.ok) {
          this.connectionStatus = { type: 'success', text: '连接正常' };
          this.addTestResult('success', '连接测试通过', '代理配置正常，可以访问工信部服务器', 'el-icon-success');
          this.currentStep = Math.max(this.currentStep, 1);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.error('连接测试失败:', error);
        this.connectionStatus = { type: 'danger', text: '连接失败' };
        this.addTestResult('danger', '连接测试失败', `错误: ${error.message}`, 'el-icon-error');
      } finally {
        this.testing.connection = false;
      }
    },
    
    /**
     * 测试短信API
     */
    async testSmsApi() {
      this.testing.sms = true;
      
      try {
        const result = await this.loginApi.getSmsCode({
          username: this.testForm.username,
          password: this.testForm.password
        });
        
        if (result.success) {
          this.addTestResult('success', '短信API测试通过', result.message, 'el-icon-message', result);
          this.currentStep = Math.max(this.currentStep, 2);
        } else {
          this.addTestResult('warning', '短信API返回错误', result.message, 'el-icon-warning', result);
        }
      } catch (error) {
        console.error('短信API测试失败:', error);
        this.addTestResult('danger', '短信API测试失败', error.message, 'el-icon-error');
      } finally {
        this.testing.sms = false;
      }
    },
    
    /**
     * 测试登录API
     */
    async testLoginApi() {
      this.testing.login = true;
      
      try {
        const result = await this.loginApi.login({
          username: this.testForm.username,
          password: this.testForm.password,
          smsCode: this.testForm.smsCode,
          toUrl: this.testForm.toUrl
        });
        
        if (result.success) {
          this.addTestResult('success', '登录API测试通过', result.message, 'el-icon-user', result);
          this.currentStep = Math.max(this.currentStep, 3);
        } else {
          this.addTestResult('warning', '登录API返回错误', result.message, 'el-icon-warning', result);
        }
      } catch (error) {
        console.error('登录API测试失败:', error);
        this.addTestResult('danger', '登录API测试失败', error.message, 'el-icon-error');
      } finally {
        this.testing.login = false;
      }
    },
    
    /**
     * 运行完整测试
     */
    async runFullTest() {
      this.testing.full = true;
      this.resetTest();
      
      try {
        this.addTestResult('info', '开始完整测试', '正在运行所有测试项目...', 'el-icon-loading');
        
        // 步骤1: 连接测试
        await this.testConnection();
        await this.delay(1000);
        
        // 步骤2: 短信API测试
        await this.testSmsApi();
        await this.delay(1000);
        
        // 步骤3: 登录API测试
        await this.testLoginApi();
        
        // 完成
        this.currentStep = 4;
        this.addTestResult('success', '完整测试完成', '所有API测试已完成，请查看详细结果', 'el-icon-circle-check');
        
      } catch (error) {
        console.error('完整测试失败:', error);
        this.addTestResult('danger', '完整测试失败', error.message, 'el-icon-error');
      } finally {
        this.testing.full = false;
      }
    },
    
    /**
     * 重置测试
     */
    resetTest() {
      this.currentStep = 0;
      this.testResults = [];
      this.connectionStatus = { type: 'info', text: '未测试' };
    },
    
    /**
     * 添加测试结果
     */
    addTestResult(type, title, message, icon, data = null) {
      this.testResults.push({
        type: type,
        title: title,
        message: message,
        icon: icon,
        data: data,
        timestamp: new Date().toLocaleTimeString()
      });
    },
    
    /**
     * 显示结果数据
     */
    showResultData(data) {
      this.selectedData = data;
      this.dataDialogVisible = true;
    },
    
    /**
     * 格式化JSON数据
     */
    formatJson(data) {
      return JSON.stringify(data, null, 2);
    },
    
    /**
     * 延迟函数
     */
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  }
};
</script>

<style scoped>
.miit-login-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.test-results {
  max-height: 500px;
  overflow-y: auto;
}

.result-data {
  margin-top: 10px;
}

.json-data {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
}

.help-content {
  line-height: 1.6;
}

.help-content h3 {
  color: #409eff;
  margin-top: 20px;
  margin-bottom: 10px;
}

.help-content ul, .help-content ol {
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 8px;
}

.help-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.el-timeline-item__content {
  padding-bottom: 20px;
}

.el-card {
  margin-bottom: 0;
}

.el-steps {
  margin: 20px 0;
}
</style>
