import axios from 'axios'
import {EamMessage, EamLoading} from '../util/EamElementExt';
axios.defaults.timeout = 1200000
// 返回其他状态吗
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500 // 默认的
}
axios.defaults.baseURL = process.env.VUE_APP_API_HOST || './'
axios.defaults.withCredentials = true

// HTTPrequest拦截
axios.interceptors.request.use(config => {
  if(config.showLoading !== false || config.stepMessage) {
    EamLoading.service(config.stepMessage) // start progress bar
  }
  config.headers['VERSION'] = process.env.VUE_APP_API_VERSION
  return config
}, error => {
  return Promise.reject(error)
})

// HTTPresponse拦截
axios.interceptors.response.use(res => {
  if(res.config.showLoading !== false) {
    console.log('close loading', res)
    EamLoading.close()
  }
  const status = Number(res.status) || 200
  if(status !== 200) {
    EamMessage({
      message: res.data.msg,
      type: 'error'
    })
  }
  return res
}, error => {
  EamLoading.close()
  console.error('error', error)
  return Promise.reject(new Error(error))
})

export default axios
